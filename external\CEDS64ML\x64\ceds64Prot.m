function [methodinfo,structs,enuminfo,ThunkLibName]=ceds64Prot
%CEDS64PROT Create structures to define interfaces found in 'ceds64int'.

%This function was generated by loadlibrary.m parser version 1.1.6.34 on Fri Aug 26 18:44:50 2016
%perl options:'ceds64int.i -outfile=ceds64Prot.m -thunkfile=ceds64Thunk.c -header=ceds64int.h'
ival={cell(1,0)}; % change 0 to the actual number of functions to preallocate the data.
structs=[];enuminfo=[];fcnNum=1;
fcns=struct('name',ival,'calltype',ival,'LHS',ival,'RHS',ival,'alias',ival,'thunkname', ival);
MfilePath=fileparts(mfilename('fullpath'));
ThunkLibName=fullfile(MfilePath,'ceds64Thunk');
%  int S64FileCount (); 
fcns.thunkname{fcnNum}='int32Thunk';fcns.name{fcnNum}='S64FileCount'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}=[];fcnNum=fcnNum+1;
%  int S64CloseAll (); 
fcns.thunkname{fcnNum}='int32Thunk';fcns.name{fcnNum}='S64CloseAll'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}=[];fcnNum=fcnNum+1;
%  int S64Create ( const char * FileName , const int nChans , const int nBig ); 
fcns.thunkname{fcnNum}='int32cstringint32int32Thunk';fcns.name{fcnNum}='S64Create'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'cstring', 'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64Open ( const char * FileName , const int nFlag ); 
fcns.thunkname{fcnNum}='int32cstringint32Thunk';fcns.name{fcnNum}='S64Open'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64IsOpen ( const int nFid ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64IsOpen'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64Close ( const int nFid ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64Close'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64Empty ( const int nFid ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64Empty'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64GetErrorMessage ( const int nErr , char * Error , const int nErrSize ); 
fcns.thunkname{fcnNum}='int32int32cstringint32Thunk';fcns.name{fcnNum}='S64GetErrorMessage'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64GetFileComment ( const int nFid , const int nInd , char * Comment , const int nGetSize ); 
fcns.thunkname{fcnNum}='int32int32int32cstringint32Thunk';fcns.name{fcnNum}='S64GetFileComment'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64SetFileComment ( const int nFid , const int nInd , const char * Comment ); 
fcns.thunkname{fcnNum}='int32int32int32cstringThunk';fcns.name{fcnNum}='S64SetFileComment'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring'};fcnNum=fcnNum+1;
%  int S64GetFreeChan ( const int nFid ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64GetFreeChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64MaxChans ( const int nFid ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64MaxChans'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  double S64GetTimeBase ( const int nFid ); 
fcns.thunkname{fcnNum}='doubleint32Thunk';fcns.name{fcnNum}='S64GetTimeBase'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='double'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64SetTimeBase ( const int nFid , const double dSecPerTick ); 
fcns.thunkname{fcnNum}='int32int32doubleThunk';fcns.name{fcnNum}='S64SetTimeBase'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'double'};fcnNum=fcnNum+1;
%  long long S64SecsToTicks ( const int nFid , const double dSec ); 
fcns.thunkname{fcnNum}='int64int32doubleThunk';fcns.name{fcnNum}='S64SecsToTicks'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'double'};fcnNum=fcnNum+1;
%  double S64TicksToSecs ( const int nFid , const long long tSec ); 
fcns.thunkname{fcnNum}='doubleint32int64Thunk';fcns.name{fcnNum}='S64TicksToSecs'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='double'; fcns.RHS{fcnNum}={'int32', 'int64'};fcnNum=fcnNum+1;
%  int S64GetVersion ( const int nFid ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64GetVersion'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  long long S64FileSize ( const int nFid ); 
fcns.thunkname{fcnNum}='int64int32Thunk';fcns.name{fcnNum}='S64FileSize'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  long long S64MaxTime ( const int nFid ); 
fcns.thunkname{fcnNum}='int64int32Thunk';fcns.name{fcnNum}='S64MaxTime'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64TimeDate ( const int nFid , long long * pTDGet , const long long * pTDSet , int iMode ); 
fcns.thunkname{fcnNum}='int32int32voidPtrvoidPtrint32Thunk';fcns.name{fcnNum}='S64TimeDate'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int64Ptr', 'int64Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64AppID ( const int nFid , int * pTDGet , const int * pTDSet , int iMode ); 
fcns.thunkname{fcnNum}='int32int32voidPtrvoidPtrint32Thunk';fcns.name{fcnNum}='S64AppID'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32Ptr', 'int32Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64GetExtraData ( const int nFid , void * pData , unsigned int nBytes , unsigned int nOffset ); 
fcns.thunkname{fcnNum}='int32int32voidPtruint32uint32Thunk';fcns.name{fcnNum}='S64GetExtraData'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'voidPtr', 'uint32', 'uint32'};fcnNum=fcnNum+1;
%  int S64SetExtraData ( const int nFid , const void * pData , unsigned int nBytes , unsigned int nOffset ); 
fcns.thunkname{fcnNum}='int32int32voidPtruint32uint32Thunk';fcns.name{fcnNum}='S64SetExtraData'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'voidPtr', 'uint32', 'uint32'};fcnNum=fcnNum+1;
%  int S64ChanType ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='int32int32int32Thunk';fcns.name{fcnNum}='S64ChanType'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  long long S64ChanDivide ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='int64int32int32Thunk';fcns.name{fcnNum}='S64ChanDivide'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  double S64GetIdealRate ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='doubleint32int32Thunk';fcns.name{fcnNum}='S64GetIdealRate'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='double'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  double S64SetIdealRate ( const int nFid , const int nChan , const double dRate ); 
fcns.thunkname{fcnNum}='doubleint32int32doubleThunk';fcns.name{fcnNum}='S64SetIdealRate'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='double'; fcns.RHS{fcnNum}={'int32', 'int32', 'double'};fcnNum=fcnNum+1;
%  int S64GetChanComment ( const int nFid , const int nChan , char * Comment , const int nGetSize ); 
fcns.thunkname{fcnNum}='int32int32int32cstringint32Thunk';fcns.name{fcnNum}='S64GetChanComment'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64SetChanComment ( const int nFid , const int nChan , const char * Comment ); 
fcns.thunkname{fcnNum}='int32int32int32cstringThunk';fcns.name{fcnNum}='S64SetChanComment'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring'};fcnNum=fcnNum+1;
%  int S64GetChanTitle ( const int nFid , const int nChan , char * Title , const int nGetSize ); 
fcns.thunkname{fcnNum}='int32int32int32cstringint32Thunk';fcns.name{fcnNum}='S64GetChanTitle'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64SetChanTitle ( const int nFid , const int nChan , const char * Title ); 
fcns.thunkname{fcnNum}='int32int32int32cstringThunk';fcns.name{fcnNum}='S64SetChanTitle'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring'};fcnNum=fcnNum+1;
%  int S64GetChanScale ( const int nFid , const int nChan , double * dScale ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrThunk';fcns.name{fcnNum}='S64GetChanScale'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'doublePtr'};fcnNum=fcnNum+1;
%  int S64SetChanScale ( const int nFid , const int nChan , const double dScale ); 
fcns.thunkname{fcnNum}='int32int32int32doubleThunk';fcns.name{fcnNum}='S64SetChanScale'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double'};fcnNum=fcnNum+1;
%  int S64GetChanOffset ( const int nFid , const int nChan , double * dOffset ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrThunk';fcns.name{fcnNum}='S64GetChanOffset'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'doublePtr'};fcnNum=fcnNum+1;
%  int S64SetChanOffset ( const int nFid , const int nChan , const double dOffset ); 
fcns.thunkname{fcnNum}='int32int32int32doubleThunk';fcns.name{fcnNum}='S64SetChanOffset'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double'};fcnNum=fcnNum+1;
%  int S64GetChanUnits ( const int nFid , const int nChan , char * Units , const int nGetSize ); 
fcns.thunkname{fcnNum}='int32int32int32cstringint32Thunk';fcns.name{fcnNum}='S64GetChanUnits'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64SetChanUnits ( const int nFid , const int nChan , const char * Units ); 
fcns.thunkname{fcnNum}='int32int32int32cstringThunk';fcns.name{fcnNum}='S64SetChanUnits'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'cstring'};fcnNum=fcnNum+1;
%  long long S64ChanMaxTime ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='int64int32int32Thunk';fcns.name{fcnNum}='S64ChanMaxTime'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  long long S64PrevNTime ( const int nFid , const int nChan , long long tFrom , long long tTo , const int n , const int nMask , const int nAsWave ); 
fcns.thunkname{fcnNum}='int64int32int32int64int64int32int32int32Thunk';fcns.name{fcnNum}='S64PrevNTime'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64', 'int64', 'int32', 'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64ChanDelete ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='int32int32int32Thunk';fcns.name{fcnNum}='S64ChanDelete'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64ChanUndelete ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='int32int32int32Thunk';fcns.name{fcnNum}='S64ChanUndelete'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64GetChanYRange ( const int nFid , const int nChan , double * dLow , double * dHigh ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrvoidPtrThunk';fcns.name{fcnNum}='S64GetChanYRange'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'doublePtr', 'doublePtr'};fcnNum=fcnNum+1;
%  int S64SetChanYRange ( const int nFid , const int nChan , const double dLow , const double dHigh ); 
fcns.thunkname{fcnNum}='int32int32int32doubledoubleThunk';fcns.name{fcnNum}='S64SetChanYRange'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double', 'double'};fcnNum=fcnNum+1;
%  int S64ItemSize ( const int nFid , const int nChan ); 
fcns.thunkname{fcnNum}='int32int32int32Thunk';fcns.name{fcnNum}='S64ItemSize'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64SetEventChan ( const int nFid , const int nChan , const double dRate , const int iType ); 
fcns.thunkname{fcnNum}='int32int32int32doubleint32Thunk';fcns.name{fcnNum}='S64SetEventChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double', 'int32'};fcnNum=fcnNum+1;
%  int S64WriteEvents ( const int nFid , const int nChan , const long long * pData , const int nCount ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32Thunk';fcns.name{fcnNum}='S64WriteEvents'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64ReadEvents ( const int nFid , const int nChan , long long * pData , int nMax , const long long tFrom , const long long tTo , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32int64int64int32Thunk';fcns.name{fcnNum}='S64ReadEvents'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64Ptr', 'int32', 'int64', 'int64', 'int32'};fcnNum=fcnNum+1;
%  int S64SetMarkerChan ( const int nFid , const int nChan , const double dRate , const int nkind ); 
fcns.thunkname{fcnNum}='int32int32int32doubleint32Thunk';fcns.name{fcnNum}='S64SetMarkerChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double', 'int32'};fcnNum=fcnNum+1;
%  int S64WriteMarkers ( const int nFid , const int nChan , const S64Marker * pData , const int count ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32Thunk';fcns.name{fcnNum}='S64WriteMarkers'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'int32'};fcnNum=fcnNum+1;
%  int S64ReadMarkers ( const int nFid , const int nChan , S64Marker * pData , const int nMax , const long long tFrom , const long long tUpto , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32int64int64int32Thunk';fcns.name{fcnNum}='S64ReadMarkers'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'int32', 'int64', 'int64', 'int32'};fcnNum=fcnNum+1;
%  int S64EditMarker ( const int nFid , const int nChan , long long t , const S64Marker * pM ); 
fcns.thunkname{fcnNum}='int32int32int32int64voidPtrThunk';fcns.name{fcnNum}='S64EditMarker'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64', 'S64MarkerPtr'};fcnNum=fcnNum+1;
%  int S64GetMaskCodes ( const int nMask , int * iCode , int * nMode ); 
fcns.thunkname{fcnNum}='int32int32voidPtrvoidPtrThunk';fcns.name{fcnNum}='S64GetMaskCodes'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32Ptr', 'int32Ptr'};fcnNum=fcnNum+1;
%  int S64SetMaskCodes ( const int nMask , const int * nCode ); 
fcns.thunkname{fcnNum}='int32int32voidPtrThunk';fcns.name{fcnNum}='S64SetMaskCodes'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32Ptr'};fcnNum=fcnNum+1;
%  int S64SetMaskMode ( const int nMask , const int nMode ); 
fcns.thunkname{fcnNum}='int32int32int32Thunk';fcns.name{fcnNum}='S64SetMaskMode'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64GetMaskMode ( const int nMask ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64GetMaskMode'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64SetMaskCol ( const int nMask , const int nCol ); 
fcns.thunkname{fcnNum}='int32int32int32Thunk';fcns.name{fcnNum}='S64SetMaskCol'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64GetMaskCol ( const int nMask ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64GetMaskCol'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64ResetMask ( const int nMask ); 
fcns.thunkname{fcnNum}='int32int32Thunk';fcns.name{fcnNum}='S64ResetMask'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
%  int S64ResetAllMasks (); 
fcns.thunkname{fcnNum}='int32Thunk';fcns.name{fcnNum}='S64ResetAllMasks'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}=[];fcnNum=fcnNum+1;
%  int S64SetLevelChan ( const int nFid , const int nChan , const double dRate ); 
fcns.thunkname{fcnNum}='int32int32int32doubleThunk';fcns.name{fcnNum}='S64SetLevelChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double'};fcnNum=fcnNum+1;
%  int S64SetInitLevel ( const int nFid , const int nChan , const int nLevel ); 
fcns.thunkname{fcnNum}='int32int32int32int32Thunk';fcns.name{fcnNum}='S64SetInitLevel'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int32'};fcnNum=fcnNum+1;
%  int S64WriteLevels ( const int nFid , const int nChan , const long long * pData , const int nCount ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32Thunk';fcns.name{fcnNum}='S64WriteLevels'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64ReadLevels ( const int nFid , const int nChan , long long * pData , int nMax , const long long tFrom , const long long tTo , int * nLevel ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32int64int64voidPtrThunk';fcns.name{fcnNum}='S64ReadLevels'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64Ptr', 'int32', 'int64', 'int64', 'int32Ptr'};fcnNum=fcnNum+1;
%  int S64SetTextMarkChan ( const int nFid , const int nChan , double dRate , const int nMax ); 
fcns.thunkname{fcnNum}='int32int32int32doubleint32Thunk';fcns.name{fcnNum}='S64SetTextMarkChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double', 'int32'};fcnNum=fcnNum+1;
%  int S64SetExtMarkChan ( const int nFid , const int nChan , double dRate , const int nType , const int nRows , const int nCols , const long long tDiv ); 
fcns.thunkname{fcnNum}='int32int32int32doubleint32int32int32int64Thunk';fcns.name{fcnNum}='S64SetExtMarkChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'double', 'int32', 'int32', 'int32', 'int64'};fcnNum=fcnNum+1;
%  int S64GetExtMarkInfo ( const int nFid , const int nChan , int * nRows , int * nCols ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrvoidPtrThunk';fcns.name{fcnNum}='S64GetExtMarkInfo'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int32Ptr', 'int32Ptr'};fcnNum=fcnNum+1;
%  int S64Write1TextMark ( const int nFid , const int nChan , const S64Marker * pData , const char * text , const int nSize ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrcstringint32Thunk';fcns.name{fcnNum}='S64Write1TextMark'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'cstring', 'int32'};fcnNum=fcnNum+1;
%  int S64Write1RealMark ( const int nFid , const int nChan , const S64Marker * pData , const float * real , const int nSize ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrvoidPtrint32Thunk';fcns.name{fcnNum}='S64Write1RealMark'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'singlePtr', 'int32'};fcnNum=fcnNum+1;
%  int S64Write1WaveMark ( const int nFid , const int nChan , const S64Marker * pData , const short * wave , const int nSize ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrvoidPtrint32Thunk';fcns.name{fcnNum}='S64Write1WaveMark'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'int16Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64Read1TextMark ( const int nFid , const int nChan , S64Marker * pData , char * text , const long long tFrom , const long long tUpto , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrcstringint64int64int32Thunk';fcns.name{fcnNum}='S64Read1TextMark'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'cstring', 'int64', 'int64', 'int32'};fcnNum=fcnNum+1;
%  int S64Read1RealMark ( const int nFid , const int nChan , S64Marker * pData , float * real , const long long tFrom , const long long tUpto , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrvoidPtrint64int64int32Thunk';fcns.name{fcnNum}='S64Read1RealMark'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'singlePtr', 'int64', 'int64', 'int32'};fcnNum=fcnNum+1;
%  int S64Read1WaveMark ( const int nFid , const int nChan , S64Marker * pData , short * real , const long long tFrom , const long long tUpto , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrvoidPtrint64int64int32Thunk';fcns.name{fcnNum}='S64Read1WaveMark'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'S64MarkerPtr', 'int16Ptr', 'int64', 'int64', 'int32'};fcnNum=fcnNum+1;
%  int S64SetWaveChan ( const int nFid , const int nChan , const long long tDiv , const int nType , const double dRate ); 
fcns.thunkname{fcnNum}='int32int32int32int64int32doubleThunk';fcns.name{fcnNum}='S64SetWaveChan'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int64', 'int32', 'double'};fcnNum=fcnNum+1;
%  long long S64WriteWaveS ( const int nFid , const int nChan , const short * pData , const int count , const long long tFrom ); 
fcns.thunkname{fcnNum}='int64int32int32voidPtrint32int64Thunk';fcns.name{fcnNum}='S64WriteWaveS'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'int32', 'int16Ptr', 'int32', 'int64'};fcnNum=fcnNum+1;
%  long long S64WriteWaveF ( const int nFid , const int nChan , const float * pData , const int count , const long long tFrom ); 
fcns.thunkname{fcnNum}='int64int32int32voidPtrint32int64Thunk';fcns.name{fcnNum}='S64WriteWaveF'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'int32', 'singlePtr', 'int32', 'int64'};fcnNum=fcnNum+1;
%  long long S64WriteWave64 ( const int nFid , const int nChan , const double * pData , const int count , const long long tFrom ); 
fcns.thunkname{fcnNum}='int64int32int32voidPtrint32int64Thunk';fcns.name{fcnNum}='S64WriteWave64'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int64'; fcns.RHS{fcnNum}={'int32', 'int32', 'doublePtr', 'int32', 'int64'};fcnNum=fcnNum+1;
%  int S64ReadWaveS ( const int nFid , const int nChan , short * pData , const int nMax , const long long tFrom , const long long tUpto , long long * tFirst , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32int64int64voidPtrint32Thunk';fcns.name{fcnNum}='S64ReadWaveS'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'int16Ptr', 'int32', 'int64', 'int64', 'int64Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64ReadWaveF ( const int nFid , const int nChan , float * pData , const int nMax , const long long tFrom , const long long tUpto , long long * tFirst , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32int64int64voidPtrint32Thunk';fcns.name{fcnNum}='S64ReadWaveF'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'singlePtr', 'int32', 'int64', 'int64', 'int64Ptr', 'int32'};fcnNum=fcnNum+1;
%  int S64ReadWave64 ( const int nFid , const int nChan , double * pData , const int nMax , const long long tFrom , const long long tUpto , long long * tFirst , const int nMask ); 
fcns.thunkname{fcnNum}='int32int32int32voidPtrint32int64int64voidPtrint32Thunk';fcns.name{fcnNum}='S64ReadWave64'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='int32'; fcns.RHS{fcnNum}={'int32', 'int32', 'doublePtr', 'int32', 'int64', 'int64', 'int64Ptr', 'int32'};fcnNum=fcnNum+1;
structs.S64Marker.members=struct('m_Time', 'int64', 'm_Code1', 'uint8', 'm_Code2', 'uint8', 'm_Code3', 'uint8', 'm_Code4', 'uint8');
methodinfo=fcns;