function fif_read_isotrak_hpi_meas(fname,VISIBLE)
%FIF_READ_ISOTRAK_HPI_MEAS
% function fif_read_isotrak_hpi_meas(fname,VISIBLE)
% meant to be run using data found in the meas_info folder of the Neuromag
% Will write out a JPG file, so better is to copy isotrak and hpi_coils.fif
% data to storage area first
% Sequence in real-time at the acquisition command line:
%      cd /neuro/dacq/meas_info
%      cp isotrak <patient storage area>
% After "measure" has been pushed, but before "accept", then
%      /neuro/dacq/bin/utility/hpifit -slopes hpi_meas.info -isotrak isotrak
% which writes out hpi_coils.def.
%      cp hpi_coils.def <patient storage area>
% then run this function with no arguments to generate "isotrak.jpg", or
% with arguments ([],1) to see in Matlab.

%% If fname is not given, assume this is for the temporary isotrak data
if ~exist('fname','var'),
   fname = [];
end

if isempty(fname),
   fname = 'isotrak';
end

if ~exist('VISIBLE','var'),
   VISIBLE = 0; % not visible
end

global FIFF;
if isempty(FIFF)
   FIFF = fiff_define_constants();
end


%% If this is the Isotrak, hard wired

if strcmp(fname,'isotrak'),


   %% Get The Isotrak Data First

   [fid,tree] = fiff_open('isotrak');  % scratch fif file at time of meas

   % Get DIG points
   isotrak = fiff_dir_tree_find(tree, FIFF.FIFFB_ISOTRAK);
   dig=struct('kind',{},'ident',{},'r',{},'coord_frame',{});
   if length(isotrak) == 1
      p = 0;
      for k = 1:isotrak.nent
         kind = isotrak.dir(k).kind;
         pos  = isotrak.dir(k).pos;
         if kind == FIFF.FIFF_DIG_POINT
            p = p + 1;
            tag = fiff_read_tag(fid,pos);
            dig(p) = tag.data;
            dig(p).coord_frame = FIFF.FIFFV_COORD_HEAD;
         end
      end
   end


   % so now we have the digitization points

   fclose(fid);

   %% Now Get the Transformation Matrix

   [fid,tree] = fiff_open('hpi_coils.fif');


   meas_info = tree.children;
   %
   %   Read measurement info
   %
   dev_head_t=[];
   ctf_head_t=[];
   meas_date=[];
   p = 0;
   for k = 1:meas_info.nent
      kind = meas_info.dir(k).kind;
      pos  = meas_info.dir(k).pos;
      switch kind
         case FIFF.FIFF_NCHAN
            tag = fiff_read_tag(fid,pos);
            nchan = tag.data;
         case FIFF.FIFF_SFREQ
            tag = fiff_read_tag(fid,pos);
            sfreq = tag.data;
         case FIFF.FIFF_CH_INFO
            p = p+1;
            tag = fiff_read_tag(fid,pos);
            chs(p) = tag.data;
         case FIFF.FIFF_LOWPASS
            tag = fiff_read_tag(fid,pos);
            lowpass = tag.data;
         case FIFF.FIFF_HIGHPASS
            tag = fiff_read_tag(fid,pos);
            highpass = tag.data;
         case FIFF.FIFF_MEAS_DATE
            tag = fiff_read_tag(fid,pos);
            meas_date = tag.data;
         case FIFF.FIFF_COORD_TRANS
            tag = fiff_read_tag(fid,pos);
            cand = tag.data;
            if cand.from == FIFF.FIFFV_COORD_DEVICE && ...
                  cand.to == FIFF.FIFFV_COORD_HEAD
               dev_head_t = cand;
            elseif cand.from == FIFF.FIFFV_MNE_COORD_CTF_HEAD && ...
                  cand.to == FIFF.FIFFV_COORD_HEAD
               ctf_head_t = cand;
            end
      end
   end
   %

   fclose(fid);

else

   %% assume the user gave a fif name and wants to see the fit
   [fid,tree] = fiff_open(fname);
   info = fiff_read_meas_info(fid,tree);

   dig = info.dig;
   dev_head_t = info.dev_head_t;

end % getting data

% so we now have dev_head_t and the dig points.


%% Transform the Helmet
fv = inner_helmet; % local subfunction

fv.vertices = (dev_head_t.trans(1:3,:)*[fv.vertices';ones(1,size(fv.vertices,1))])';


%% Get a head shape
Loc = [dig.r];
fvh = hsdig2fv(Loc',5,5/1000,45*pi/180,0);


%% Option: I like helmet fixed, head rotated

Rinv = inv(dev_head_t.trans);

fv.vertices = (Rinv(1:3,:)*[fv.vertices';ones(1,size(fv.vertices,1))])';
fvh.vertices = (Rinv(1:3,:)*[fvh.vertices';ones(1,size(fvh.vertices,1))])';
Loc = Rinv(1:3,:)*[Loc;ones(1,size(Loc,2))];

%% Visualize

h = windfind('Helmet Fit');
figure(h)

if VISIBLE,
   set(h,'visible','on')
else
   set(h,'visible','off')
end

clf
for i = 1:4,
   subplot(2,2,i)

   plot3(Loc(1,:),Loc(2,:),Loc(3,:),'g*');
   patch(fv,'edgecolor','y','facecolor','y','facealpha',.3);
   patch(fvh,'edgecolor',.5*[1 1 1],'facecolor',.5*[1 1 1],'facealpha',.5);
   axis equal
   axis vis3d
   zoom(1.5)
   switch i
      case 1
         view(180,0)
         title(sprintf('File: %s',fname),'interpreter','none');
      case 2
         view(-90,0)
         title(sprintf('Date: %s',datestr(now)))
      case 3
         view(180,-90)
         title(sprintf('Head Center: %.1f, %.1f, %.1f (mm)', Rinv(1:3,4)*1000));
      case 4
         view(-145,20)
         rotate3d on
   end
end

% orient landscape

[ignore,rootname] = fileparts(fname);

saveas(h,[rootname,'.jpg']);
% saveas(h,[rootname,'.fig']);


function fv = inner_helmet;

fv.vertices = [...
   -0.0161    0.0163    0.0901
   0.0000    0.0359    0.0891
   0.0000   -0.0022    0.0910
   0.0163    0.0163    0.0901
   0.0886    0.0348   -0.0752
   0.0003    0.1285    0.0006
   0.0001   -0.0920   -0.0752
   -0.0887    0.0348   -0.0752
   -0.0693    0.0189    0.0586
   0.0001    0.1014    0.0579
   -0.0009   -0.0652    0.0587
   0.0694    0.0170    0.0586
   -0.0115    0.0315    0.0893
   -0.0116    0.0021    0.0908
   0.0117    0.0021    0.0908
   0.0116    0.0315    0.0893
   0.0854    0.0519   -0.0739
   0.0836    0.0578   -0.0644
   0.0819    0.0627   -0.0521
   0.0790    0.0702   -0.0336
   0.0746    0.0799   -0.0137
   0.0658    0.0964   -0.0062
   0.0541    0.1107   -0.0028
   0.0387    0.1199   -0.0003
   0.0196    0.1260    0.0008
   0.0188   -0.0896   -0.0752
   0.0373   -0.0833   -0.0751
   0.0534   -0.0739   -0.0751
   0.0666   -0.0606   -0.0751
   0.0774   -0.0441   -0.0752
   0.0852   -0.0260   -0.0752
   0.0893   -0.0068   -0.0752
   0.0902    0.0138   -0.0752
   -0.0902    0.0138   -0.0752
   -0.0894   -0.0067   -0.0752
   -0.0852   -0.0260   -0.0752
   -0.0774   -0.0441   -0.0751
   -0.0666   -0.0606   -0.0751
   -0.0534   -0.0740   -0.0751
   -0.0372   -0.0834   -0.0751
   -0.0187   -0.0896   -0.0752
   -0.0199    0.1260    0.0004
   -0.0395    0.1196   -0.0006
   -0.0547    0.1101   -0.0030
   -0.0662    0.0957   -0.0063
   -0.0749    0.0794   -0.0144
   -0.0792    0.0696   -0.0349
   -0.0819    0.0628   -0.0521
   -0.0834    0.0584   -0.0632
   -0.0853    0.0524   -0.0736
   -0.0666    0.0406    0.0586
   -0.0596    0.0620    0.0587
   -0.0507    0.0803    0.0579
   -0.0374    0.0933    0.0572
   -0.0199    0.1003    0.0572
   -0.0218   -0.0626    0.0585
   -0.0409   -0.0551    0.0585
   -0.0541   -0.0419    0.0587
   -0.0633   -0.0235    0.0587
   -0.0683   -0.0032    0.0586
   0.0682   -0.0058    0.0584
   0.0624   -0.0260    0.0586
   0.0525   -0.0437    0.0589
   0.0391   -0.0563    0.0585
   0.0197   -0.0631    0.0585
   0.0207    0.0999    0.0575
   0.0387    0.0920    0.0577
   0.0520    0.0786    0.0579
   0.0603    0.0597    0.0589
   0.0672    0.0395    0.0582
   -0.0209    0.1217    0.0189
   -0.0214    0.1105    0.0447
   0.0317    0.0188    0.0861
   0.0506    0.0177    0.0762
   0.0047    0.0239    0.0906
   0.0000    0.0114    0.0915
   -0.0047    0.0239    0.0906
   0.0513    0.0959    0.0434
   0.0639    0.0963    0.0144
   -0.0633    0.0972    0.0141
   -0.0478    0.0992    0.0435
   0.0771    0.0266    0.0462
   0.0856    0.0367    0.0202
   0.0856    0.0466    0.0006
   0.0821    0.0605   -0.0150
   -0.0831    0.0576   -0.0206
   -0.0853    0.0485   -0.0072
   -0.0857    0.0449    0.0068
   -0.0848    0.0374    0.0231
   -0.0768    0.0280    0.0466
   0.0739   -0.0172    0.0469
   0.0795   -0.0302    0.0230
   0.0779   -0.0405    0.0045
   0.0731   -0.0500   -0.0136
   0.0671   -0.0591   -0.0334
   0.0606   -0.0672   -0.0544
   -0.0892    0.0268   -0.0554
   -0.0897    0.0158   -0.0372
   -0.0895    0.0081   -0.0190
   -0.0892    0.0037    0.0024
   -0.0858   -0.0089    0.0209
   -0.0742   -0.0169    0.0466
   -0.0879   -0.0118   -0.0174
   -0.0821   -0.0327   -0.0185
   -0.0742   -0.0483   -0.0220
   -0.0644   -0.0622   -0.0179
   -0.0495   -0.0760   -0.0154
   -0.0311   -0.0850   -0.0140
   -0.0113   -0.0902   -0.0132
   0.0081   -0.0906   -0.0128
   0.0275   -0.0862   -0.0129
   0.0456   -0.0784   -0.0131
   0.0607   -0.0664   -0.0134
   -0.0394   -0.0818   -0.0348
   -0.0291   -0.0862   -0.0549
   -0.0892   -0.0032   -0.0371
   -0.0856   -0.0232   -0.0371
   -0.0784   -0.0412   -0.0379
   -0.0685   -0.0574   -0.0375
   -0.0558   -0.0714   -0.0359
   -0.0468   -0.0782   -0.0554
   -0.0900    0.0052   -0.0561
   -0.0614   -0.0664   -0.0560
   -0.0879   -0.0148   -0.0562
   -0.0822   -0.0336   -0.0564
   -0.0732   -0.0509   -0.0564
   -0.0205   -0.0886   -0.0341
   -0.0011   -0.0914   -0.0337
   0.0181   -0.0892   -0.0335
   0.0369   -0.0829   -0.0334
   0.0536   -0.0732   -0.0333
   0.0089   -0.0911   -0.0545
   0.0279   -0.0867   -0.0544
   0.0457   -0.0789   -0.0544
   -0.0101   -0.0910   -0.0546
   -0.0216   -0.0864    0.0051
   -0.0305   -0.0798    0.0220
   -0.0357   -0.0667    0.0467
   -0.0110   -0.0843    0.0215
   0.0073   -0.0846    0.0217
   0.0262   -0.0808    0.0228
   0.0442   -0.0740    0.0229
   0.0591   -0.0631    0.0221
   0.0705   -0.0483    0.0221
   0.0366   -0.0814    0.0054
   0.0175   -0.0873    0.0057
   -0.0018   -0.0892    0.0060
   0.0669   -0.0580    0.0044
   0.0534   -0.0720    0.0049
   0.0322   -0.0680    0.0469
   0.0138   -0.0729    0.0462
   -0.0169   -0.0725    0.0461
   -0.0015   -0.0763    0.0420
   0.0483   -0.0596    0.0465
   0.0668   -0.0360    0.0464
   0.0597   -0.0507    0.0426
   -0.0790   -0.0333    0.0199
   -0.0664   -0.0551    0.0202
   -0.0498   -0.0711    0.0215
   -0.0413   -0.0795    0.0040
   -0.0852   -0.0223    0.0017
   -0.0592   -0.0672    0.0021
   -0.0748   -0.0468   -0.0021
   -0.0527   -0.0556    0.0466
   -0.0658   -0.0379    0.0464
   -0.0841    0.0549   -0.0405
   -0.0862    0.0459   -0.0327
   -0.0870    0.0409   -0.0202
   -0.0878    0.0343   -0.0060
   -0.0884    0.0259    0.0074
   -0.0870    0.0152    0.0226
   -0.0776    0.0059    0.0464
   -0.0889    0.0253   -0.0205
   -0.0874    0.0418   -0.0615
   -0.0851    0.0520   -0.0564
   -0.0871    0.0426   -0.0470
   -0.0883    0.0330   -0.0355
   -0.0891    0.0194   -0.0065
   0.0775    0.0046    0.0465
   0.0825   -0.0314   -0.0135
   0.0880   -0.0109   -0.0138
   0.0895    0.0105   -0.0146
   0.0883    0.0311   -0.0156
   0.0860    0.0461   -0.0182
   0.0894    0.0206   -0.0366
   0.0900    0.0062   -0.0559
   0.0822   -0.0336   -0.0546
   0.0857   -0.0226   -0.0340
   0.0880   -0.0142   -0.0550
   0.0894   -0.0016   -0.0348
   0.0782   -0.0416   -0.0336
   0.0728   -0.0514   -0.0544
   0.0894    0.0252   -0.0580
   0.0883    0.0345   -0.0461
   0.0875    0.0388   -0.0319
   0.0845    0.0530   -0.0310
   0.0837    0.0568   -0.0435
   0.0863    0.0462   -0.0446
   0.0875    0.0410   -0.0590
   0.0850    0.0526   -0.0563
   0.0854   -0.0208    0.0050
   0.0853   -0.0090    0.0229
   0.0872    0.0136    0.0222
   0.0889    0.0011    0.0050
   0.0887    0.0233    0.0041
   -0.0806    0.0641    0.0015
   -0.0808    0.0573    0.0203
   -0.0733    0.0486    0.0463
   -0.0625    0.0803    0.0432
   -0.0743    0.0760    0.0178
   -0.0730    0.0825    0.0029
   -0.0706    0.0651    0.0406
   0.0634    0.0785    0.0432
   0.0743    0.0760    0.0178
   0.0793    0.0678    0.0027
   0.0810    0.0579    0.0183
   0.0739    0.0475    0.0457
   0.0712    0.0643    0.0401
   0.0726    0.0832    0.0034
   -0.0452    0.1139    0.0161
   -0.0347    0.1119    0.0353
   -0.0324    0.1040    0.0490
   0.0165    0.1136    0.0411
   0.0292    0.1201    0.0169
   0.0128    0.1241    0.0160
   -0.0021    0.1203    0.0300
   0.0474    0.1123    0.0166
   0.0347    0.1072    0.0434
   0.0254    0.0435    0.0850
   0.0386    0.0529    0.0781
   0.0485    0.0582    0.0703
   -0.0312   -0.0100    0.0854
   -0.0433   -0.0121    0.0792
   -0.0544   -0.0110    0.0715
   -0.0304    0.0038    0.0867
   -0.0235    0.0846    0.0690
   -0.0220    0.0730    0.0760
   -0.0232    0.0624    0.0802
   -0.0204    0.0478    0.0851
   -0.0464    0.0587    0.0717
   -0.0334    0.0565    0.0794
   -0.0564    0.0071    0.0718
   -0.0563    0.0249    0.0715
   -0.0528    0.0424    0.0716
   -0.0442    0.0315    0.0793
   -0.0333    0.0229    0.0853
   -0.0461    0.0176    0.0791
   -0.0457    0.0032    0.0793
   -0.0340    0.0141    0.0853
   -0.0270    0.0321    0.0864
   -0.0397    0.0449    0.0794
   -0.0254    0.0428    0.0851
   -0.0350    0.0731    0.0719
   -0.0105    0.0491    0.0858
   0.0253    0.0857    0.0678
   0.0155    0.0746    0.0762
   0.0083    0.0610    0.0823
   0.0028    0.0484    0.0862
   0.0009    0.0733    0.0774
   -0.0129    0.0751    0.0762
   0.0094    0.0881    0.0686
   -0.0081    0.0879    0.0688
   -0.0024    0.0493    0.0860
   -0.0071    0.0625    0.0818
   0.0005    0.0591    0.0831
   -0.0155    0.0653    0.0803
   0.0091    0.0457    0.0868
   0.0294    0.0587    0.0800
   0.0279    0.0723    0.0749
   0.0391    0.0672    0.0727
   0.0370    0.0804    0.0669
   0.0185    0.0611    0.0814
   0.0183    0.0473    0.0855
   0.0265   -0.0172    0.0855
   0.0283   -0.0346    0.0790
   0.0266   -0.0484    0.0710
   0.0149   -0.0215    0.0867
   0.0103   -0.0531    0.0704
   -0.0066   -0.0534    0.0705
   -0.0233   -0.0497    0.0709
   -0.0379   -0.0414    0.0714
   -0.0482   -0.0272    0.0718
   -0.0374   -0.0259    0.0788
   -0.0266   -0.0359    0.0789
   -0.0121   -0.0420    0.0781
   0.0013   -0.0455    0.0766
   0.0143   -0.0415    0.0782
   -0.0260   -0.0183    0.0854
   -0.0142   -0.0220    0.0866
   0.0006   -0.0314    0.0842
   0.0494   -0.0236    0.0722
   0.0383   -0.0237    0.0791
   0.0312   -0.0084    0.0857
   0.0428   -0.0093    0.0800
   0.0537   -0.0063    0.0729
   0.0294    0.0058    0.0871
   0.0433    0.0054    0.0808
   0.0403   -0.0387    0.0717
   0.0219    0.0312    0.0878
   0.0450    0.0463    0.0760
   0.0464    0.0336    0.0778
   0.0357    0.0395    0.0823
   0.0304    0.0277    0.0860
   0.0536    0.0471    0.0697];

fv.faces = [...
   16     2    75
   14    76     1
   76    75    77
   75     2    77
   2    13    77
   76    77     1
   77    13     1
   16    75     4
   14     3    76
   15    76     3
   15     4    76
   4    75    76
   8    34    97
   61    62    91
   94   113    95
   41   115    40
   40   121    39
   34    35   122
   39   123    38
   35    36   124
   36    37   125
   119   118   126
   118   125   126
   125    37   126
   119   126   123
   37    38   126
   126    38   123
   36   125   124
   118   117   125
   125   117   124
   35   124   122
   117   116   124
   124   116   122
   119   123   120
   39   121   123
   123   121   120
   34   122    97
   116    98   122
   122    98    97
   120   121   114
   40   115   121
   121   115   114
   98   116    99
   114   107   120
   107   106   120
   99   116   103
   116   117   103
   120   106   119
   106   105   119
   103   117   104
   117   118   104
   119   105   118
   118   105   104
   107   114   108
   114   115   127
   95   131    96
   128   132   129
   132    26   133
   26    27   133
   96   134    28
   133    27   134
   27    28   134
   132   133   129
   96   131   134
   131   130   134
   129   133   130
   133   134   130
   26   132     7
   115    41   135
   41     7   135
   7   132   135
   115   135   127
   132   128   135
   135   128   127
   114   127   108
   95   113   131
   113   112   131
   108   127   109
   127   128   109
   131   112   130
   112   111   130
   109   128   110
   128   129   110
   130   111   129
   129   111   110
   99   103   100
   57   138    56
   137   136   139
   92   144    93
   112   145   111
   145   141   146
   141   140   146
   136   147   139
   146   140   147
   140   139   147
   145   146   111
   136   109   147
   109   110   147
   111   146   110
   146   147   110
   141   145   142
   93   144   148
   144   143   148
   145   149   142
   148   143   149
   143   142   149
   93   148    94
   145   112   149
   112   113   149
   94   148   113
   148   149   113
   142   150   141
   65    11   151
   56   152    11
   152   139   153
   139   140   153
   140   151   153
   152   153    11
   153   151    11
   139   152   137
   56   138   152
   152   138   137
   65   151   150
   140   141   151
   151   141   150
   65   150    64
   64   154    63
   62    63   155
   154   143   156
   143   144   156
   144   155   156
   154   156    63
   156   155    63
   62   155    91
   144    92   155
   155    92    91
   143   154   142
   64   150   154
   154   150   142
   109   136   108
   108   160   107
   103   104   161
   107   162   106
   161   104   163
   104   105   163
   162   163   106
   105   106   163
   161   163   157
   162   158   163
   163   158   157
   158   162   159
   107   160   162
   162   160   159
   103   161   100
   157   101   161
   161   101   100
   159   160   137
   108   136   160
   160   136   137
   101   157   102
   137   138   159
   159   164   158
   164    58   165
   58    59   165
   59   102   165
   164   165   158
   102   157   165
   165   157   158
   58   164    57
   159   138   164
   164   138    57
   59    60   102
   60   172   102
   102   172   101
   172   171   101
   101   171   100
   171   170   100
   99   173    98
   48   175   166
   175   174   176
   173   177    98
   177    97    98
   97   176   174
   97   177   176
   175   176   166
   173   168   177
   168   167   177
   166   176   167
   176   177   167
   97   174     8
   48    49   175
   8   174    50
   174   175    50
   175    49    50
   168   173   169
   170   169   178
   169   173   178
   173    99   178
   170   178   100
   178    99   100
   60     9   172
   9    90   172
   48   166    47
   172    90   171
   90    89   171
   171    89   170
   89    88   170
   170    88   169
   88    87   169
   169    87   168
   166    86    47
   166   167    86
   86   168    87
   86   167   168
   28    29    96
   183   182   185
   30    31   187
   187   189   188
   188   189   190
   189   186   190
   186   185   190
   188   190   181
   185   182   190
   190   182   181
   186   189    32
   187    31   189
   189    31    32
   181   180   188
   188   191   187
   187   191   192
   191    95   192
   95    96   192
   187   192    30
   96    29   192
   192    29    30
   95   191    94
   188   180   191
   191   180    94
   32    33   186
   33     5   193
   184   195   196
   5    17   199
   198   199   200
   199    17   200
   17    18   200
   198   200   197
   18    19   200
   200    19   197
   5   199   193
   198   194   199
   199   194   193
   194   198   195
   197   196   198
   198   196   195
   184   196    85
   19    20   197
   85   196    20
   196   197    20
   33   193   186
   184   183   195
   183   185   195
   186   193   185
   193   194   185
   194   195   185
   94   180    93
   85    84   184
   180   181   201
   202   204   203
   203   205    83
   83   205    84
   205   183    84
   183   184    84
   183   205   182
   203   204   205
   205   204   182
   182   204   181
   202   201   204
   204   201   181
   202   203   179
   83    82   203
   203    82   179
   180   201    93
   179    91   202
   91    92   202
   93   201    92
   201   202    92
   91   179    61
   82    12   179
   179    12    61
   47    86    46
   9    51    90
   51   208    90
   46    86   206
   86    87   206
   90   208    89
   208   207    89
   89   207    88
   206    88   207
   206    87    88
   51    52   208
   54    81    53
   53    81   209
   81    80   209
   209    80   210
   210   211   206
   206   211    46
   45    46   211
   45   211    80
   211   210    80
   53   209    52
   206   207   210
   209   210   212
   210   207   212
   207   208   212
   209   212    52
   212   208    52
   85    20    21
   67    68    78
   12    82    70
   68    69   213
   85   215    84
   70   217    69
   217   216   218
   216   214   218
   214   213   218
   217   218    69
   218   213    69
   216   217    83
   70    82   217
   217    82    83
   214   216   215
   83    84   216
   216    84   215
   68   213    78
   85    21   215
   78   213    79
   213   214    79
   214   215   219
   215    21   219
   21    22   219
   214   219    79
   219    22    79
   220    81   221
   54    55   222
   55    72   222
   72   221   222
   54   222    81
   222   221    81
   72    71   221
   43    71    42
   43   220    71
   220   221    71
   220    43    44
   45    80    44
   81   220    80
   44    80   220
   224    24    25
   72   226    71
   71     6    42
   71   226     6
   226   225     6
   225   226   223
   226    10   223
   226    72    10
   72    55    10
   6   225    25
   223   224   225
   225   224    25
   66    67   228
   67    78   228
   22    23    79
   23   227    79
   228    78   227
   78    79   227
   23    24   227
   24   224   227
   10    66   223
   66   228   223
   227   224   228
   224   223   228
   69    68   231
   232    14   235
   14     1   235
   232   235   233
   55    54   236
   244   243   245
   233   235   248
   1   246   249
   246   247   249
   247   248   249
   1   249   235
   249   248   235
   233   248   234
   247   242   248
   248   242   234
   242   247   243
   246   245   247
   247   245   243
   246     1   250
   1    13   250
   246   250   245
   245   250   251
   13   239   252
   239   241   252
   241   251   252
   13   252   250
   252   251   250
   245   251   244
   241   240   251
   251   240   244
   234   242    60
   240    52   244
   52    51   244
   60   242     9
   242   243     9
   244    51   243
   243    51     9
   52   240    53
   239   238   241
   53   240   253
   240   241   253
   241   238   253
   238   237   253
   53   253    54
   237   236   253
   253   236    54
   239    13   254
   13     2   254
   239   254   238
   67    66   255
   237   260   236
   66    10   261
   260   259   262
   259   261   262
   261    10   262
   260   262   236
   10    55   262
   262    55   236
   66   261   255
   259   256   261
   261   256   255
   256   259   257
   2   258   263
   260   264   259
   264   263   265
   263   258   265
   258   257   265
   264   265   259
   265   257   259
   2   263   254
   263   264   254
   264   260   266
   260   237   266
   237   238   266
   264   266   254
   266   238   254
   258     2   267
   2    16   267
   258   267   257
   229   230   268
   231    68   270
   67   255   271
   255   269   271
   269   270   271
   67   271    68
   271   270    68
   231   270   230
   269   268   270
   270   268   230
   255   256   269
   272   267   273
   267    16   273
   16   229   273
   229   268   273
   269   272   268
   273   268   272
   267   272   257
   269   256   272
   272   256   257
   60    59   234
   274    15   277
   15     3   277
   274   277   275
   234   282   233
   283   281   284
   281   280   284
   275   287   276
   284   280   285
   280   279   285
   276   287   278
   287   286   278
   285   279   286
   279   278   286
   275   277   287
   3    14   289
   14   288   289
   288   284   289
   284   285   289
   3   289   290
   289   285   290
   285   286   290
   3   290   277
   277   290   287
   290   286   287
   284   288   283
   14   232   288
   288   232   283
   281   283   282
   232   233   283
   283   233   282
   276   278    65
   234    59   282
   59    58   282
   65   278    11
   278   279    11
   282    58   281
   58    57   281
   11   279    56
   279   280    56
   281    57   280
   280    57    56
   65    64   276
   15   274   293
   274   292   293
   61   295    62
   293   292   294
   292   291   294
   62   295   291
   291   295   294
   4    15   296
   15   293   296
   293   294   296
   4   296    73
   294   295   297
   295    74   297
   74    73   297
   294   297   296
   297    73   296
   295    61    74
   74    61    12
   62   291    63
   274   275   292
   276    64   298
   64    63   298
   63   291   298
   276   298   275
   291   292   298
   298   292   275
   229    16   299
   16     4   299
   74   301    73
   73   301   303
   303   302   299
   303   301   302
   301   300   302
   73   303     4
   303   299     4
   299   302   229
   229   302   230
   302   300   230
   230   300   231
   69   304    70
   70    74    12
   74    70   301
   70   304   301
   301   304   300
   69   231   304
   304   231   300];

function h = windfind(name);
% function h = windfind(name);
% find the first window whos 'Name' is name.
% If no such window exists, create it.
% h is the handle of the window returned.

% Copyright(c) 1994 John C. Mosher
% Los Alamos National Laboratory
% Group ESA-6, MS J580
% Los Alamos, NM 87545
% email: <EMAIL>

% 3/3/94 Author

if(~isstr(name)),
   error('WINDFIND: input argument must be string');
end

hw = get(0,'children');		% all open windows
hw = sort(hw);			% in increasing window order

for i = 1:length(hw),
   s = get(hw(i),'Name');
   if(strcmp(deblank(s),deblank(name))),
      h = hw(i);
      return;
   end
end

% we exited out without a match, make the window
h = figure;
set(h,'Name',name,'visible','off');


