function Labels = mri_getlabels_marsatlas()
% ATLAS     : MarsAtlas (Auzias 2016)
% REFERENCE : http://www.meca-brain.org/softwares/cortical-parcellation-marsatlas/

% @=============================================================================
% This function is part of the Brainstorm software:
% https://neuroimage.usc.edu/brainstorm
% 
% Copyright (c) University of Southern California & McGill University
% This software is distributed under the terms of the GNU General Public License
% as published by the Free Software Foundation. Further details on the GPLv3
% license can be found at http://www.gnu.org/copyleft/gpl.html.
% 
% FOR RESEARCH PURPOSES ONLY. THE SOFTWARE IS PROVIDED "AS IS," AND THE
% UNIVERSITY OF SOUTHERN CALIFORNIA AND ITS COLLABORATORS DO NOT MAKE ANY
% WARRANTY, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF
% MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE, NOR DO THEY ASSUME ANY
% LIABILITY OR RESPONSIBILITY FOR THE USE OF THIS SOFTWARE.
%
% For more information type "brainstorm license" at command prompt.
% =============================================================================@

Labels = {...
    0,   'Unknown',       [255   255   255]; ...
    1,   'VCcm L',        [255     0     0]; ...
    101, 'VCcm R',        [255     0     0]; ...
    2,   'VCl L',         [  0   255     0]; ...
    102, 'VCl R',         [  0   255     0]; ...
    3,   'VCs L',         [239   119    41]; ...
    103, 'VCs R',         [239   119    41]; ...
    4,   'Cu L',          [210    25   245]; ...
    104, 'Cu R',          [210    25   245]; ...
    5,   'VCrm L',        [167   117    74]; ...
    105, 'VCrm R',        [167   117    74]; ...
    6,   'ITCm L',        [255   218    47]; ...
    106, 'ITCm R',        [255   218    47]; ...
    7,   'ITCr L',        [  0    30   200]; ...
    107, 'ITCr R',        [  0    30   200]; ...
    8,   'MTCc L',        [145   250   250]; ...
    108, 'MTCc R',        [145   250   250]; ...
    9,   'STCc L',        [255   144   195]; ...
    109, 'STCc R',        [255   144   195]; ...
    10,  'STCr L',        [250   250   145]; ...
    110, 'STCr R',        [250   250   145]; ...
    11,  'MTCr L',        [147   147   179]; ...
    111, 'MTCr R',        [147   147   179]; ...
    12,  'ICC L',         [ 23   250    23]; ...
    112, 'ICC R',         [ 23   250    23]; ...
    13,  'IPCv L',        [253   238     5]; ...
    113, 'IPCv R',        [253   238     5]; ...
    14,  'IPCd L',        [229    24   254]; ...
    114, 'IPCd R',        [229    24   254]; ...
    15,  'SPC L',         [  0   250     0]; ...
    115, 'SPC R',         [  0   250     0]; ...
    16,  'SPCm L',        [125   100    50]; ...
    116, 'SPCm R',        [125   100    50]; ...
    17,  'PCm L',         [ 66    15   175]; ...
    117, 'PCm R',         [ 66    15   175]; ...
    18,  'PCC L',         [255   212    79]; ...
    118, 'PCC R',         [255   212    79]; ...
    19,  'Sv L',          [  5   218    41]; ...
    119, 'Sv R',          [  5   218    41]; ...
    20,  'Sdl L',         [ 19   219   243]; ...
    120, 'Sdl R',         [ 19   219   243]; ...
    21,  'Sdm L',         [250     0     0]; ...
    121, 'Sdm R',         [250     0     0]; ...
    22,  'Mv L',          [ 48    36    96]; ...
    122, 'Mv R',          [ 48    36    96]; ...
    23,  'Mdl L',         [168   159   174]; ...
    123, 'Mdl R',         [168   159   174]; ...
    24,  'Mdm L',         [255    83   219]; ...
    124, 'Mdm R',         [255    83   219]; ...
    25,  'PMrv L',        [248   188   124]; ...
    125, 'PMrv R',        [248   188   124]; ...
    26,  'PMdl L',        [250     0   250]; ...
    126, 'PMdl R',        [250     0   250]; ...
    27,  'PMdm L',        [250   250     0]; ...
    127, 'PMdm R',        [250   250     0]; ...
    28,  'PFcdl L',       [  0   250     0]; ...
    128, 'PFcdl R',       [  0   250     0]; ...
    29,  'PFcdm L',       [255   126     0]; ...
    129, 'PFcdm R',       [255   126     0]; ...
    30,  'MCC L',         [ 66    15     6]; ...
    130, 'MCC R',         [ 66    15     6]; ...
    31,  'PFrvl L',       [  0     0   250]; ...
    131, 'PFrvl R',       [  0     0   250]; ...
    32,  'Pfrdli L',      [ 88    43     1]; ...
    132, 'Pfrdli R',      [ 88    43     1]; ...
    33,  'Pfrdls L',      [250     0     0]; ...
    133, 'Pfrdls R',      [250     0     0]; ...
    34,  'PFrd L',        [  0   173     0]; ...
    134, 'PFrd R',        [  0   173     0]; ...
    35,  'PFrm L',        [250   250     0]; ...
    135, 'PFrm R',        [250   250     0]; ...
    36,  'OFCvl L',       [109   196    24]; ...
    136, 'OFCvl R',       [109   196    24]; ...
    37,  'OFCv L',        [188    43   143]; ...
    137, 'OFCv R',        [188    43   143]; ...
    38,  'OFCvm L',       [210   231     0]; ...
    138, 'OFCvm R',       [210   231     0]; ...
    39,  'PFCvm L',       [188   143     1]; ...
    139, 'PFCvm R',       [188   143     1]; ...
    40,  'ACC L',         [  0    42   255]; ...
    140, 'ACC R',         [  0    42   255]; ...
    41,  'Insula L',      [ 18   243   243]; ...
    141, 'Insula R',      [ 18   243   243]; ...
    210, 'Thalamus L',    [255     0     0]; ...
    249, 'Thalamus R',    [255     0     0]; ...
    211, 'Caudate L',     [147   147   179]; ...
    250, 'Caudate R',     [147   147   179]; ...
    212, 'Putamen L',     [ 23   250    23]; ...
    251, 'Putamen R',     [ 23   250    23]; ...
    213, 'Pallidum L',    [253   238     5]; ...
    252, 'Pallidum R',    [253   238     5]; ...
    217, 'Hippocampus L', [229    24   254]; ...
    253, 'Hippocampus R', [229    24   254]; ...
    218, 'Amygdala L',    [  0   250     0]; ...
    254, 'Amygdala R',    [  0   250     0]; ...
    226, 'Accumbens L',   [125   100    50]; ...
    258, 'Accumbens R',   [125   100    50]; ...
    255, 'XIgnore',       [255   255   255]; ...
};
