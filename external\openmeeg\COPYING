The OpenMEEG software is a C++ package for solving the forward
problem of electroencephalography (EEG) and magnetoencephalography (MEG).

OpenMEEG is distributed under the French opensource license CeCILL-B. It is
intended to give users the freedom to modify and redistribute the software.
It is therefore compatible with popular opensource licenses such as the GPL
and BSD licenses. The CeCILL-B license imposes to anybody distributing a
software incorporating OpenMEEG the obligation to give credits (by citing the
appropriate publications), in order for all contributions to be properly
identified and acknowledged.

The references to be acknowledged are :

Gramfort et al. OpenMEEG: opensource software for quasistatic
bioelectromagnetics. Biomedical engineering online (2010) vol. 9 (1) pp. 45
http://www.biomedical-engineering-online.com/content/9/1/45
doi:10.1186/1475-925X-9-45

<PERSON><PERSON><PERSON> et al. Generalized head models for MEG/EEG: boundary element method
beyond nested volumes. Phys. Med. Biol. (2006) vol. 51 pp. 1333-1346
doi:10.1088/0031-9155/51/5/021

Contact : <EMAIL>
Web : http://openmeeg.gforge.inria.fr
