April 2025
- Anatomy: Add support for PET volumes
- Process: Apply montages on raw continuous recordings
- SEEG/ECOG: Improvements for iEEG implantation: Add/remove individual contacts
- Events: Allow event processes in record panel for Matrix files
- Events: Add process to uniform event colors in Protocol
- Distrib: Update neuroimage.usc.edu URLs to HTTPS
--------------------------------------------------------------
March 2025
- Anatomy: Export surface scouts as MRI mask
- Anatomy: Improved isosurface threshold slider
- TimeFreq: Correct sign of imaginary part in complex TF computed with Hilbert
- Distrib: Improved detection of write permissions in Windows symlinks
--------------------------------------------------------------
February 2025
- Anatomy: Import MRI BESA volumes and surfaces
- Anatomy: Export surfaces as STL files
- Process: Add frequency band support to process PSD features
- Process: Add z-score output to process Compare to normative PSD
- Process: GUI, handle nested controller-classes in process options
- Distrib: Compilation with Matlab 2025a
--------------------------------------------------------------
December 2024
- Anatomy: Copy/Paste scout operations
- Anatomy: Scalp scouts from sensor positions
- Distrib: Compilation with Matlab 2024a
- Distrib: Compilation with Matlab 2024b
--------------------------------------------------------------
November 2024
- SEEG/ECOG: Allow iEEG implantation using MRI and/or CT and/or IsoSurface
- Registration: Manual and auto localization of EEG sensors with 3D scanner mesh
- Registration: Project EEG sensors for default caps in Colin27 def anat
- Anatomy: Add skull stripping in Brainstorm (uses SPM or BrainSuite)
- Anatomy: Add template 'Colin27 4NIRS' (2024)
- IO: Process to merge (channel-wise) raw continue recordings
- Distrib: Update script for epilepsy tutorial, add source estimation with MEM
- Distrib: Script to run the BEst (Brain Entropy in space and time) tutorial
- Plugins: iso2mesh, brain2mesh and xdf now support Apple Silicon
--------------------------------------------------------------
October 2024
- Anatomy: Spatial correlations and brain annotations: 'bst-neuromaps' plugin
- Anatomy: Support exporting mixed sources as NIfTI
- Registration: Allow selecting and deleting head points
- TimeFreq: Add model selection to FOOOF and SPRiNT
- IO: Support of EDF files with multiple sampling rates (using FielTrip plugin)
- Bugfix: Importing MEG sensor locations from FieldTrip data
--------------------------------------------------------------
September 2024
- Artifacts: ICA, display explaned variance for ICs
- Distrib: Function 'test_tutorial.m' and GitHub workflow 'run_tutorial.yaml'
- Plugins: Bugfix, interaction between SPM and FieldTrip
- Plugins: Bugfix, always read FIFF with functions in 'brainstorm3/external'
- GUI: Show/hide hidden files (Linux, Windows and macOS)
--------------------------------------------------------------
August 2024
- Anatomy: Improve volume computation, use 'boundary'
- Coreg: New digitization panel
- SEEG/ECOG: Improvements and bugfix for IEEG panel
- IO: EDF+ support negative gains
- Distrib: Linux and macOS, find Matlab runtime in full Matlab installation
- Distrib: Add deep-brain-activity script
- Distrib: Add deviation-maps tutorial
--------------------------------------------------------------
July 2024
- Anatomy: Import output from FreeSurfer recon-all-clinical
- Bugfix: Importing non-Atlas MRI volumes given in MNI space
- Inverse: Process to apply exclusion zone around sensors for volume grids
- Artifacts: New process for automatic detection of artifacts on MEG
- Plugins: Add mTRF-Toolbox and Zeffiro
- Plugins: OpenMEEG and MCXLAB now support Apple Silicon
- Distrib: Menu option to retrieve system information
--------------------------------------------------------------
June 2024
- Anatomy: Bugfix, set proper subtype on importing Fibers and FEM
- Viz: Allow combination of resection options in Fig3D
- Viz: MRI viewer contactsheets support overlayed Atlases and Volumes
- Viz: Improved figure positioning for Windows11
- IO: SNIRF, improve import of Landmarks and Events
- IO: Intan RHS, bugfix, incorrect block selection
- IO: Neuralyn, bugfix reading NSE files, add support for NTT files
- Distrib: Add brain fingerprinting tutorial script and page
- Distrib: Brainstorm compiled with Matlab 2023a
--------------------------------------------------------------
May 2024
- Anatomy: Bugfix at selecting proper int type when exporting MRI as NIfTI
- Viz: Improved contactsheets from MRIviewer and 3D slices
- IO: Updated support for Open Ephys
- IO: Support double, complex-float and complex-double .fif data
- Events: Added "Every sample" option for extended2simple conversion
- Plugins: Remove npy-matlab code from Brainstorm, add it as plugin
- Plugins: Allow user registering plugins as supported plugins in Brainstorm
--------------------------------------------------------------
April 2024
- Anatomy: Added AAL1 MNI parcellation
- PSD: Added 2D layout visualization
- Artifacts: Detect bad channels: peak-to-peak for continuous data
- Distrib: Update Matlab RunTime path binary in macOS and Linux
--------------------------------------------------------------
March 2024
- SEEG/ECOG: Improved contact localization using 3D figures
- SEEG/ECOG: Revamped iEEG panel
- Anatomy: New scout operations (intersect and duplicate)
- IO: Improved support for ITAB files
- IO: Bugfix on importing Curry .pom channel files
- Events: Allow digital mask for events from channel
--------------------------------------------------------------
February 2024
- Database: Integration of CT volumes and their options in database explorer
- Database: Links to raw files are updated if disk letter or mount point changes
- IO: Syncronize raw data with common event
--------------------------------------------------------------
January 2024
- IO: Import channel-wise events from BrainVision BrainAmp
--------------------------------------------------------------
December 2023
- Bugfix: Keep BadTrial flags when merging Studies
- Plugin: Improve handling of processes from installed plugins
- Distrib: Basic support Apple silicon (OsType `mac64arm`)
- IO: Add process Export to file
--------------------------------------------------------------
November 2023
- Plugin:  CT2MRIREG to perform CT to MRI co-registration
- IO: Import Brainstorm sources
- Plugins: Remove EASYH5 and JSNIRF code from Brainstorm, add them as plugins
- Bugfix: Export EDF+ with UTF-8 encoding
- IO: Export to .xlsx files for Matlab >= R2019a
- IO: Export EEG as Brainsight format
--------------------------------------------------------------
October 2023
- Distrib: Compilation with Matlab 2023a/2023b
- Bugfix: Merging events with, and events without 'channels' and 'notes'
- Connectivity: Reorganize different correlation options
- iEEG: Save, Load, Export and Import electrode models
--------------------------------------------------------------
September 2023
- Anatomy: Import resection mask from BrainSuite SVReg
- IO: Export raw data as FieldTrip structure
--------------------------------------------------------------
August 2023
- Scouts: Add 'power' as scout function
- Reports: Send compact report by email if requested
- Anatomy: Display anatomical atlases on 3D orthogonal slices view
- Connectivity: Reorganize connectivity metrics
- Connectivity: Fix across-trials average of phase metrics
- Distrib: Add workflow to run tutorials GitHub runners
--------------------------------------------------------------
July 2023
- Anatomy : Export scouts as FreeSurfer annotations
- New process: Remove evoke response
- Bugfix: (e-phys) Error computing tuning curves
--------------------------------------------------------------
June 2023
- PCA: Major fixes and improvements for Scouts and Flattening unconstrained sources
|  New tutorial page. Fix 1st PC sign inconsistency across epochs and conditions
|  PCA for scouts rescaled. "% power kept" fixed and saved in history
- Connectivity: New option to flatten unconstrained sources with PCA
- Connectivity: Fix scouts on volume and mixed models
--------------------------------------------------------------
May 2023
- FOOOF: Add option to display model only for freq range of analysis
- Plugin: Get Blackrock NPMK library from its GitHub HEAD
- Anatomy: Support of FreeSurfer wfiles as textures
- Bugfix: Error in averaging PAC and tPAC
--------------------------------------------------------------
April 2023
- NIRS: Project channel files between subjects
- 3DViewer: Overlay any type of surfaces
--------------------------------------------------------------
March 2023
- Parallel computing: Handle temporary files per process, to avoid conflicts
- Recordings: Optional storage of events channels and notes
- Anatomy: New template ICBM152 2023b: New BEM and cortex for OpenMEEG
- Anatomy: Contralateral projection of volume scouts
--------------------------------------------------------------
February 2023
- Stat: Added FieldTrip threshold-free cluster enhancement (TFCE)
- Anatomy: Compute FEM mesh statistics
- Anatomy: FEM mesh generation with SimNIBS4/CHARM
- Anatomy: Menu to merge/rename/delete FEM layers
- Anatomy: Fixes in template ICBM152 2023 (T1 contrast & tissue labels)
- Bugfix: Error in data covariance computation: Baseline not removed
| when baseline time window was not included in data time window
--------------------------------------------------------------
January 2023
- Anatomy: New template ICBM152 2023
- Anatomy: Remove selection of fiducials when setting anatomy template
- Anatomy: Project scouts between hemispheres using the anatomy template
- Distrib: Enable compilation on Linux and MacOS
- IO: Export matrix files as EDF+
- Inverse: Display scouts on source-level PSD as PSD figures
- Clusters: Save clusters in channel file, load automatically
- Clusters: Import from process
- Distrib: Brainstorm compiled with Matlab 2022b
--------------------------------------------------------------
November 2022
- Forward: Display leadfield sensitivity (surface, MRI, isosurface)
- BIDS import: Added support for ACPC and CapTrak coordinate systems
- BIDS import: Added support for NIRS
- Database: References to .bst files without full path, for portability
- Anatomy: New template ICBM152 2022
--------------------------------------------------------------
October 2022
- EEG: Project channel files between subjects
- Inverse: Project dipoles files between subjects
- BIDS import: Added support for AssociatedEmptyRoom + compute noise covariance
--------------------------------------------------------------
September 2022
- Registration: Color-coding of digitized head points based on distance to scalp
- IO: Updated Nicolet EEG reader
- IO: Support for BIOPAC AcqKnowledge .acq recordings
- IO: Support for XDF EEG recordings
- ICA: Integration of Picard algorithm
- ICA: Integration of FastICA algorithm
- EEG templates: Convert MNI to subject space (menu "Add EEG positions")
--------------------------------------------------------------
August 2022
- Anatomy: Project scouts between hemispheres (FreeSurfer option contrasurfreg)
- IO: Support for BCI2000 .dat recordings
--------------------------------------------------------------
July 2022
- Doc: Updated SEEG/epileptogenicity tutorial (new BIDS dataset)
--------------------------------------------------------------
June 2022
- Connectivity: New slider to threshold by percentile in Display tab
- Connectivity: Deprecation of older AEC processes, replaced with HENV
- GUI: Keep file selection when navigating between tabs
- MRI Viewer: Setting fiducials on the head surface
- BIDS import: Added support for IntendedFor in _coordsystem.json
- BIDS import: Added support for anatomical landmarks (_t1w.json and _coordsystem.json)
- Distrib: Compilation with Matlab 2022b
- Anatomy: Create surfaces from MRI volume atlas
- IO: Export BIDS _electrodes.tsv, with the choice of the reference MRI
--------------------------------------------------------------
May 2022
- IO: Fixed export to Plotly
- New process: Threshold by percentile
--------------------------------------------------------------
April 2022
- 2D Layout: Added option "Flip Y axis"
- Distrib: Compilation with Matlab 2022a
- IO: Import channel from ASCII files now expect mm instead of cm
- IO: Add export events in AnyWave .mrk format
- Ephys: Updated electrophysiology functions and tutorial
--------------------------------------------------------------
March 2022
- Connectivity: Corticomuscular coherence tutorial
- Web server: New SMTP server on neuroimage.usc.edu
--------------------------------------------------------------
February 2022
- Anatomy: Extract head with FSL/BET
- Connectivity: Added wPLI and ciPLV
- FOOOF/SPRiNT: New display options for 3D figures and MRI viewer
--------------------------------------------------------------
January 2022
- Doc: Connectivity tutorial
- Connectivity: Updated "Simulate AR signals"
- Connectivity: Removed the p-values thresholding from all connectivity metrics
- Plugins: Archive software environment (brainstorm + plugins + defaults)
- IO: Added support for Neuroelectrics EEG with EEGLAB plugin (.easy, .nedf)
- IO: Updated Plexon reader
- Statistics: Add process "Conjunction inference"
- SPRiNT: Spectral Parameterization Resolved in Time
--------------------------------------------------------------
December 2021
- IO: Added support for Tobii Pro Glasses export .tsv
- IO: Added support for FieldTrip trialinfo field
- IO: Updated York Instrument MEGSCAN reader (.meghdf5)
- Anatomy: Add support for InfantFS / infant_recon_all
- SimMEEG: Video tutorials
- Distrib: Removed JAVA3D/JOGL support + older connectivity graph display
- Plugins: Save GitHub commit SHA and installation date
--------------------------------------------------------------
November 2021
- Anatomy: MRI segmentation with FastSurfer (T1 only)
- Anatomy: MRI segmentation with FreeSurfer (T1+T2)
- Ephys: DeriveLFP now available as a plugin and in the compiled distribution
- Automatic registration: Add a tolerance parameter for excluding outliers
- GUI: 3D MRI display: Moving slice by clicking on it
- GUI: Automatic registration reports the distance headpoints/scalp
- Plugins: Display usage statistics
- Reports: Send HTML report by email
--------------------------------------------------------------
October 2021
- Anatomy: Run MRI segmentation with BrainSuite
- Connectivity: Updated simulation of AR signals
- OpenMEEG: Updated download URLs
- Import BIDS: Add support for CAT12, BrainSuite and BrainVISA
--------------------------------------------------------------
September 2021
- SEEG/ECOG contact labelling from volume and surface atlases
- Distrib: Compilation with Matlab 2021a/2021b
- Distrib: Updated deployment and compilation scripts
- Anatomy: Connectivity display with fibers in subject space
- New plugin: MIA
- Bugfix: Restoring mean after the PCA (scouts or unconstrained>flat)
--------------------------------------------------------------
August 2021
- Doc: New FEM tutorials
---------------------------------------------------------------
July 2021
- Inverse: FieldTrip DICS
- Anatomy: Fixed O'Reilly infant templates
- IO: Added support for Axion AxIS recordings (.raw)
- Connectivity: New coherence processes 2021
---------------------------------------------------------------
June 2021
- New connectivity graphs
- CAT12: Added support for thalamus atlas
- Anatomy: Tissues tesselation with tess_meshlayer
- IO: Added Blackrock NPMK libary as plugin
- Doc: Updated introduction tutorial dataset to FreeSurfer 7.1
---------------------------------------------------------------
May 2021
- Anatomy: Use imported volume parcellations as volume scouts
- Anatomy: Updated CAT12 to version 12.8 (r1825)
- IO: Support for Micromed .EVT
---------------------------------------------------------------
April 2021
- Plugins: NIRSTORM included as a plugin (available as compiled)
- Plugins: BrainEntropy included as a plugin (available as compiled)
- Doc: Updated all the tutorials with the new anatomy tools
- Events: Marking a trial as good removes the tag "bad" from all the events
- Anatomy: Import BrainSuite multiple parcellations
---------------------------------------------------------------
March 2021
- New plugins manager: first release
- Distrib: Reorganized compilation system to include plugins
- IO: Bugfix export .nii qform/sform, especially after resampling
- Anatomy: Mindboggle atlas renamed DKT atlas
---------------------------------------------------------------
February 2021
- SimMEEG: Simulate MEG/EEG signals with SimMEEG
- Distrib: Continued development of the plugin manager
---------------------------------------------------------------
January 2021
- Anatomy: MNI normalization with SPM12 Segment
- Anatomy: Prepared a list of standard MNI atlases: AAL2, AAL3, AICHA, 
| Brainnetome, Hammers, Neuromorphometrics, Julich-Brain, Schaefer2018
- Distrib: New plugin manager bst_plugin
- IO: Added support for ADInstruments LabChart EEG (.adicht)
- IO: Added again NWB support
