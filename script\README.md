# EEG/MEG Time Series Visualization

This package provides a MATLAB function for visualizing EEG/MEG time series data in a style similar to Brainstorm software.

## Files

- `function.m` - Main visualization function
- `demo_eeg_meg_visualization.m` - Demonstration script with examples
- `README.md` - This documentation file

## Main Function: `visualize_eeg_meg_timeseries`

### Syntax

```matlab
hFig = visualize_eeg_meg_timeseries(data, time_vector, channel_names)
hFig = visualize_eeg_meg_timeseries(data, time_vector, channel_names, 'param', value, ...)
```

### Inputs

**Required:**
- `data` - [nChannels × nTime] matrix of EEG/MEG data
- `time_vector` - [1 × nTime] vector of time points (in seconds)
- `channel_names` - {nChannels × 1} cell array of channel names

**Optional Parameters:**
- `'DisplayMode'` - 'butterfly' (default) or 'column'
- `'Modality'` - 'EEG' (default), 'MEG', 'MEG MAG', 'MEG GRAD', 'SEEG', 'ECOG'
- `'FlipYAxis'` - true/false - flip Y axis (negative values up)
- `'AutoScale'` - true (default)/false - auto-scale Y axis
- `'ShowLegend'` - true/false - show channel legend (auto-detect if empty)
- `'ShowGrid'` - true/false - show grid lines
- `'ShowGFP'` - true/false - show Global Field Power
- `'LineColors'` - [nChannels × 3] matrix of RGB colors or 'auto'
- `'DisplayUnits'` - string - units for display (auto-detect if empty)
- `'Title'` - string - figure title
- `'FigureHandle'` - existing figure handle to plot in

### Output

- `hFig` - handle to the created figure

## Features

### Display Modes

1. **Butterfly Mode**: All channels overlaid in a single plot
   - Good for seeing overall signal patterns
   - Supports Global Field Power (GFP) display
   - Optional legend for small numbers of channels

2. **Column Mode**: Channels displayed in separate rows
   - Good for examining individual channel activity
   - Channel names displayed as Y-axis labels
   - Automatic spacing between channels

### Automatic Unit Detection

The function automatically detects appropriate display units based on data amplitude and modality:

- **EEG/ECOG/SEEG**: Volts → microVolts (µV) or milliVolts (mV)
- **MEG**: Tesla → femtoTesla (fT) or picoTesla (pT)
- **Other**: No scaling applied

### Color Schemes

- **Auto**: Intelligent color assignment based on channel count and type
- **Custom**: User-defined RGB color matrix
- **Special handling**: NIRS channels (HbO=red, HbR=blue, HbT=green)

### Global Field Power (GFP)

When enabled, displays the standard deviation across channels at each time point:
- Shown as a gray line at the bottom of butterfly plots
- Only displayed when there are more than 5 channels
- Useful for identifying periods of high brain activity

## Usage Examples

### Basic EEG Visualization

```matlab
% Load your data
data = your_eeg_data;  % [64 × 1000] matrix
time = (0:999)/500;    % 2 seconds at 500 Hz
channels = {'Fp1', 'Fp2', 'F3', 'F4', ...};  % Channel names

% Create butterfly plot
hFig = visualize_eeg_meg_timeseries(data, time, channels);
```

### MEG with Custom Settings

```matlab
% MEG data visualization
hFig = visualize_eeg_meg_timeseries(meg_data, time, meg_channels, ...
                                   'Modality', 'MEG', ...
                                   'DisplayMode', 'butterfly', ...
                                   'ShowGFP', true, ...
                                   'Title', 'MEG Evoked Response');
```

### Column Display with Grid

```matlab
% Column display for detailed channel inspection
hFig = visualize_eeg_meg_timeseries(data, time, channels, ...
                                   'DisplayMode', 'column', ...
                                   'ShowGrid', true, ...
                                   'FlipYAxis', true);
```

### Custom Colors

```matlab
% Define custom colors for each channel
colors = jet(length(channels));  % Or any [nChannels × 3] matrix

hFig = visualize_eeg_meg_timeseries(data, time, channels, ...
                                   'LineColors', colors, ...
                                   'ShowLegend', true);
```

## Running the Demo

Execute the demonstration script to see various visualization examples:

```matlab
run('demo_eeg_meg_visualization.m')
```

This will create 6 different figures showing:
1. Basic EEG butterfly plot
2. EEG column display with grid
3. EEG butterfly with Global Field Power and legend
4. MEG butterfly plot with automatic unit scaling
5. EEG column display with custom colors
6. SEEG column display with flipped Y-axis

## Data Storage

Each figure stores metadata in its application data that can be accessed for further processing:

```matlab
ts_info = getappdata(hFig, 'TimeSeriesInfo');
```

This structure contains:
- Original data and parameters
- Scaling factors and units
- Channel information
- Display settings

## Compatibility

- Requires MATLAB R2016b or later (for `contains` function)
- No additional toolboxes required
- Compatible with Brainstorm data structures

## Brainstorm Integration

This function mimics the visualization style of Brainstorm software:
- Similar color schemes and scaling
- Butterfly and column display modes
- Global Field Power calculation
- Automatic unit detection
- Y-axis flipping for neurophysiology conventions

## Tips

1. **Performance**: For large datasets, consider downsampling time points for display
2. **Colors**: Use 'auto' for automatic color assignment, especially with many channels
3. **Units**: Let the function auto-detect units unless you have specific requirements
4. **GFP**: Enable Global Field Power for butterfly plots with many channels
5. **Column mode**: Use for detailed inspection of individual channels
6. **Grid**: Enable grid lines for easier time/amplitude reading

## Troubleshooting

**Common Issues:**
- Ensure data dimensions match: [nChannels × nTime]
- Time vector length must equal data time dimension
- Channel names count must match data channel dimension
- For custom colors, provide [nChannels × 3] RGB matrix

**Performance:**
- Large datasets (>1000 channels or >100k time points) may be slow
- Consider data decimation for display purposes
- Use 'column' mode sparingly with many channels
