function hFig = visualize_eeg_meg_timeseries(data, time_vector, channel_names, varargin)
% VISUALIZE_EEG_MEG_TIMESERIES: Display EEG/MEG time series in Brainstorm style
%
% USAGE: hFig = visualize_eeg_meg_timeseries(data, time_vector, channel_names, ...)
%        hFig = visualize_eeg_meg_timeseries(data, time_vector, channel_names, 'param', value, ...)
%
% INPUTS:
%    - data         : [nChannels x nTime] matrix of EEG/MEG data
%    - time_vector  : [1 x nTime] vector of time points (in seconds)
%    - channel_names: {nChannels x 1} cell array of channel names
%
% OPTIONAL PARAMETERS:
%    - 'DisplayMode'    : 'butterfly' (default) or 'column'
%    - 'Modality'       : 'EEG' (default), 'MEG', 'MEG MAG', 'MEG GRAD', 'SEEG', 'ECOG'
%    - 'FlipYAxis'      : true/false - flip Y axis (negative values up)
%    - 'AutoScale'      : true (default)/false - auto-scale Y axis
%    - 'ShowLegend'     : true/false - show channel legend (auto-detect if empty)
%    - 'ShowGrid'       : true/false - show grid lines
%    - 'ShowGFP'        : true/false - show Global Field Power
%    - 'LineColors'     : [nChannels x 3] matrix of RGB colors or 'auto'
%    - 'DisplayUnits'   : string - units for display (auto-detect if empty)
%    - 'Title'          : string - figure title
%    - 'FigureHandle'   : existing figure handle to plot in
%
% OUTPUT:
%    - hFig : handle to the created figure
%
% EXAMPLES:
%    % Basic butterfly plot
%    hFig = visualize_eeg_meg_timeseries(data, time, channels);
%    
%    % Column display with custom parameters
%    hFig = visualize_eeg_meg_timeseries(data, time, channels, ...
%                                       'DisplayMode', 'column', ...
%                                       'Modality', 'EEG', ...
%                                       'ShowGFP', true);

% Parse input arguments
p = inputParser;
addRequired(p, 'data', @(x) isnumeric(x) && ismatrix(x));
addRequired(p, 'time_vector', @(x) isnumeric(x) && isvector(x));
addRequired(p, 'channel_names', @(x) iscell(x) || ischar(x) || isstring(x));

addParameter(p, 'DisplayMode', 'butterfly', @(x) ismember(lower(x), {'butterfly', 'column'}));
addParameter(p, 'Modality', 'EEG', @(x) ismember(upper(x), {'EEG', 'MEG', 'MEG MAG', 'MEG GRAD', 'SEEG', 'ECOG'}));
addParameter(p, 'FlipYAxis', [], @(x) islogical(x) || isnumeric(x));
addParameter(p, 'AutoScale', true, @(x) islogical(x) || isnumeric(x));
addParameter(p, 'ShowLegend', [], @(x) islogical(x) || isnumeric(x));
addParameter(p, 'ShowGrid', false, @(x) islogical(x) || isnumeric(x));
addParameter(p, 'ShowGFP', false, @(x) islogical(x) || isnumeric(x));
addParameter(p, 'LineColors', 'auto', @(x) isnumeric(x) || ischar(x) || isstring(x));
addParameter(p, 'DisplayUnits', '', @(x) ischar(x) || isstring(x));
addParameter(p, 'Title', '', @(x) ischar(x) || isstring(x));
addParameter(p, 'FigureHandle', [], @(x) isempty(x) || ishandle(x));

parse(p, data, time_vector, channel_names, varargin{:});

% Extract parsed parameters
data = p.Results.data;
time_vector = p.Results.time_vector(:)';  % Ensure row vector
channel_names = p.Results.channel_names;
display_mode = lower(p.Results.DisplayMode);
modality = upper(p.Results.Modality);
flip_y_axis = p.Results.FlipYAxis;
auto_scale = p.Results.AutoScale;
show_legend = p.Results.ShowLegend;
show_grid = p.Results.ShowGrid;
show_gfp = p.Results.ShowGFP;
line_colors = p.Results.LineColors;
display_units = p.Results.DisplayUnits;
fig_title = p.Results.Title;
hFig = p.Results.FigureHandle;

% Validate inputs
[nChannels, nTime] = size(data);
if length(time_vector) ~= nTime
    error('Time vector length must match data time dimension');
end

% Convert channel names to cell array if needed
if ischar(channel_names) || isstring(channel_names)
    if ischar(channel_names)
        channel_names = cellstr(channel_names);
    else
        channel_names = cellstr(string(channel_names));
    end
end

if length(channel_names) ~= nChannels
    error('Number of channel names must match data channel dimension');
end

% Set default parameters based on modality
if isempty(flip_y_axis)
    flip_y_axis = ismember(modality, {'EEG', 'MEG', 'MEG GRAD', 'MEG MAG', 'SEEG', 'ECOG'});
end

% Auto-detect legend display
if isempty(show_legend)
    show_legend = (nChannels <= 15) && ~ismember(modality, {'EEG', 'MEG', 'MEG MAG', 'MEG GRAD', 'SEEG', 'ECOG'});
end

% Get display units and scaling factor
[data_scaled, scale_factor, units] = get_display_units(data, modality, display_units);

% Create or use existing figure
if isempty(hFig)
    hFig = figure('Name', sprintf('%s Time Series', modality), ...
                  'NumberTitle', 'off', ...
                  'Color', 'white', ...
                  'Units', 'normalized', ...
                  'Position', [0.1 0.1 0.8 0.6]);
else
    figure(hFig);
    clf;
end

% Create axes
hAxes = axes('Parent', hFig, ...
             'Box', 'on', ...
             'FontSize', 10, ...
             'Units', 'normalized', ...
             'Position', [0.08 0.08 0.85 0.85]);

% Set line colors
if ischar(line_colors) && strcmp(line_colors, 'auto')
    line_colors = generate_line_colors(nChannels, modality, channel_names);
elseif isnumeric(line_colors) && size(line_colors, 1) ~= nChannels
    error('Line colors matrix must have same number of rows as channels');
end

% Plot data based on display mode
switch display_mode
    case 'butterfly'
        plot_butterfly_mode(hAxes, time_vector, data_scaled, channel_names, ...
                           line_colors, show_legend, show_gfp, flip_y_axis, units);
    case 'column'
        plot_column_mode(hAxes, time_vector, data_scaled, channel_names, ...
                        line_colors, show_legend, flip_y_axis, units);
end

% Configure axes
configure_axes(hAxes, time_vector, auto_scale, show_grid, flip_y_axis, units, fig_title);

% Store information in figure for potential callbacks
setappdata(hFig, 'TimeSeriesInfo', struct(...
    'data', data, ...
    'time_vector', time_vector, ...
    'channel_names', {channel_names}, ...
    'modality', modality, ...
    'display_mode', display_mode, ...
    'scale_factor', scale_factor, ...
    'units', units, ...
    'flip_y_axis', flip_y_axis));

end


%% ===== GET DISPLAY UNITS =====
function [data_scaled, scale_factor, units] = get_display_units(data, modality, display_units)
% Get appropriate display units and scaling factor based on data and modality

% Calculate maximum absolute value
max_val = max(abs(data(:)));

% If display units are specified, use them
if ~isempty(display_units)
    scale_factor = 1;
    units = display_units;
    data_scaled = data;
    return;
end

% Auto-detect units based on modality and data range
switch upper(modality)
    case {'EEG', 'ECOG', 'SEEG', 'EOG', 'ECG', 'EMG'}
        % EEG data in Volts, displayed in microVolts or milliVolts
        if max_val < 0.01
            scale_factor = 1e6;
            units = '\muV';
        elseif max_val < 1
            scale_factor = 1e3;
            units = 'mV';
        else
            scale_factor = 1;
            units = 'No units';
        end

    case {'MEG', 'MEG MAG', 'MEG GRAD'}
        % MEG data in Tesla, displayed in femtoTesla or picoTesla
        if max_val < 1e-9
            scale_factor = 1e15;
            units = 'fT';
        elseif max_val < 1e-6
            scale_factor = 1e12;
            units = 'pT';
        else
            scale_factor = 1;
            units = 'No units';
        end

    otherwise
        % Default: no scaling
        scale_factor = 1;
        units = 'No units';
end

% Apply scaling
data_scaled = data * scale_factor;

end


%% ===== GENERATE LINE COLORS =====
function colors = generate_line_colors(nChannels, modality, channel_names)
% Generate appropriate line colors for channels

% Default color for single line or small number of channels
default_color = [0.2 0.2 0.2];

if nChannels == 1
    colors = default_color;
    return;
end

% Generate color table similar to Brainstorm
% Use a colormap that provides good contrast
if nChannels <= 7
    % Use distinct colors for small number of channels
    color_map = [
        0.0000 0.4470 0.7410;  % Blue
        0.8500 0.3250 0.0980;  % Orange
        0.9290 0.6940 0.1250;  % Yellow
        0.4940 0.1840 0.5560;  % Purple
        0.4660 0.6740 0.1880;  % Green
        0.3010 0.7450 0.9330;  % Light Blue
        0.6350 0.0780 0.1840;  % Red
    ];
    colors = color_map(1:nChannels, :);
else
    % Use colormap for larger number of channels
    colors = hsv(nChannels);
end

% Special handling for NIRS channels (HbO, HbR, HbT)
if any(contains(channel_names, 'HbO', 'IgnoreCase', true)) || ...
   any(contains(channel_names, 'HbR', 'IgnoreCase', true)) || ...
   any(contains(channel_names, 'HbT', 'IgnoreCase', true))

    % Find HbO, HbR, HbT channels
    iHbO = find(contains(channel_names, 'HbO', 'IgnoreCase', true));
    iHbR = find(contains(channel_names, 'HbR', 'IgnoreCase', true));
    iHbT = find(contains(channel_names, 'HbT', 'IgnoreCase', true));

    % Assign specific colors
    if ~isempty(iHbO)
        colors(iHbO, :) = repmat([1 0 0], length(iHbO), 1); % Red for HbO
    end
    if ~isempty(iHbR)
        colors(iHbR, :) = repmat([0 0 1], length(iHbR), 1); % Blue for HbR
    end
    if ~isempty(iHbT)
        colors(iHbT, :) = repmat([0 1 0], length(iHbT), 1); % Green for HbT
    end
end

end


%% ===== PLOT BUTTERFLY MODE =====
function plot_butterfly_mode(hAxes, time_vector, data, channel_names, line_colors, show_legend, show_gfp, flip_y_axis, units)
% Plot all channels overlaid in butterfly mode

hold(hAxes, 'on');

% Plot each channel
nChannels = size(data, 1);
hLines = zeros(nChannels, 1);

for iChan = 1:nChannels
    if size(line_colors, 1) == 1
        color = line_colors;
    else
        color = line_colors(iChan, :);
    end

    hLines(iChan) = plot(hAxes, time_vector, data(iChan, :), ...
                        'Color', color, ...
                        'LineWidth', 1, ...
                        'Tag', 'DataLine');
end

% Add legend if requested
if show_legend && nChannels <= 15
    legend(hAxes, hLines, channel_names, ...
           'Location', 'eastoutside', ...
           'FontSize', 8, ...
           'Interpreter', 'none');
end

% Plot Global Field Power if requested
if show_gfp && nChannels > 5
    plot_gfp(hAxes, time_vector, data, flip_y_axis);
end

% Set Y-axis direction
if flip_y_axis
    set(hAxes, 'YDir', 'reverse');
end

hold(hAxes, 'off');

end


%% ===== PLOT COLUMN MODE =====
function plot_column_mode(hAxes, time_vector, data, channel_names, line_colors, show_legend, flip_y_axis, units)
% Plot channels in separate rows (column mode)

hold(hAxes, 'on');

nChannels = size(data, 1);
nTime = size(data, 2);

% Calculate channel offsets for column display
max_range = max(max(data, [], 2) - min(data, [], 2));
channel_spacing = max_range * 1.2;
channel_offsets = (0:(nChannels-1)) * channel_spacing;

% Flip offsets if Y-axis is flipped
if flip_y_axis
    channel_offsets = channel_offsets(end:-1:1);
end

% Plot each channel with offset
hLines = zeros(nChannels, 1);
for iChan = 1:nChannels
    if size(line_colors, 1) == 1
        color = line_colors;
    else
        color = line_colors(iChan, :);
    end

    y_data = data(iChan, :) + channel_offsets(iChan);
    hLines(iChan) = plot(hAxes, time_vector, y_data, ...
                        'Color', color, ...
                        'LineWidth', 1, ...
                        'Tag', 'DataLine');
end

% Set Y-tick labels to channel names
if flip_y_axis
    ytick_positions = channel_offsets(end:-1:1);
    ytick_labels = channel_names(end:-1:1);
else
    ytick_positions = channel_offsets;
    ytick_labels = channel_names;
end

set(hAxes, 'YTick', ytick_positions, ...
           'YTickLabel', ytick_labels, ...
           'FontSize', 8);

% Set Y-axis limits
y_margin = channel_spacing * 0.1;
ylim(hAxes, [min(channel_offsets) - y_margin, max(channel_offsets) + y_margin]);

hold(hAxes, 'off');

end


%% ===== PLOT GLOBAL FIELD POWER =====
function plot_gfp(hAxes, time_vector, data, flip_y_axis)
% Plot Global Field Power (GFP) at the bottom of the axes

% Calculate GFP (standard deviation across channels)
gfp = std(data, 1);
max_gfp = max(gfp);

if max_gfp <= 0
    return;
end

% Get current axes limits
ylim_current = get(hAxes, 'YLim');

% Scale GFP to fit at bottom of plot (8% of Y range)
gfp_scaled = gfp ./ max_gfp .* (ylim_current(2) - ylim_current(1)) .* 0.08 + ylim_current(1) * 0.95;

% Flip if needed
if flip_y_axis
    gfp_scaled = ylim_current(2) - gfp_scaled + ylim_current(1);
end

% Plot GFP
plot(hAxes, time_vector, gfp_scaled, ...
     'Color', [0.5 0.5 0.5], ...
     'LineWidth', 1.5, ...
     'Tag', 'GFP');

% Add GFP label
text_x = 0.01 * time_vector(end) + 0.99 * time_vector(1);
text_y = max(gfp_scaled);
text(hAxes, text_x, text_y, 'GFP', ...
     'FontSize', 8, ...
     'Color', [0.5 0.5 0.5], ...
     'Tag', 'GFPLabel');

end


%% ===== CONFIGURE AXES =====
function configure_axes(hAxes, time_vector, auto_scale, show_grid, flip_y_axis, units, fig_title)
% Configure axes properties

% Set X-axis properties
xlabel(hAxes, 'Time (s)', 'FontSize', 10);
xlim(hAxes, [min(time_vector), max(time_vector)]);

% Set Y-axis label
if ~isempty(units) && ~strcmp(units, 'No units')
    ylabel(hAxes, sprintf('Amplitude (%s)', units), 'FontSize', 10);
else
    ylabel(hAxes, 'Amplitude', 'FontSize', 10);
end

% Grid
if show_grid
    grid(hAxes, 'on');
    set(hAxes, 'GridAlpha', 0.3);
else
    grid(hAxes, 'off');
end

% Title
if ~isempty(fig_title)
    title(hAxes, fig_title, 'FontSize', 12, 'Interpreter', 'none');
end

% Auto-scale Y-axis if requested
if auto_scale
    % Let MATLAB auto-scale
    axis(hAxes, 'tight');
end

% Set other axes properties
set(hAxes, 'Box', 'on', ...
           'FontSize', 10, ...
           'TickDir', 'out');

% Add zero line if appropriate
ylim_current = get(hAxes, 'YLim');
if ylim_current(1) < 0 && ylim_current(2) > 0
    line(hAxes, [min(time_vector), max(time_vector)], [0, 0], ...
         'Color', [0.7 0.7 0.7], ...
         'LineStyle', '--', ...
         'LineWidth', 0.5, ...
         'Tag', 'ZeroLine');
end

end
