%% DEMO_EEG_MEG_VISUALIZATION: Demonstration of EEG/MEG time series visualization
%
% This script demonstrates how to use the visualize_eeg_meg_timeseries function
% to create Brainstorm-style visualizations of EEG/MEG data.

%% Clear workspace
% clear; close all; clc;

%% ===== GENERATE SAMPLE EEG DATA =====
fprintf('Generating sample EEG data...\n');

% Parameters
fs = 500;           % Sampling frequency (Hz)
duration = 2;       % Duration (seconds)
nChannels = 8;      % Number of EEG channels
nTime = fs * duration;

% Time vector
time_vector = (0:nTime-1) / fs;

% Channel names (standard 10-20 system)
channel_names = {'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4'};

% Generate realistic EEG-like signals
data = zeros(nChannels, nTime);
for iChan = 1:nChannels
    % Base signal with alpha rhythm (8-12 Hz)
    alpha_freq = 8 + 4*rand();  % Random alpha frequency
    alpha_signal = sin(2*pi*alpha_freq*time_vector) * (20e-6);  % 20 µV amplitude
    
    % Add some beta activity (13-30 Hz)
    beta_freq = 13 + 17*rand();
    beta_signal = sin(2*pi*beta_freq*time_vector) * (5e-6);   % 5 µV amplitude
    
    % Add noise
    noise = randn(1, nTime) * (2e-6);  % 2 µV RMS noise
    
    % Add some slow drift
    drift = sin(2*pi*0.5*time_vector) * (10e-6);  % 0.5 Hz drift
    
    % Combine signals
    data(iChan, :) = alpha_signal + beta_signal + noise + drift;
    
    % Add some channel-specific variations
    data(iChan, :) = data(iChan, :) * (0.5 + rand());  % Random amplitude scaling
end

% Add an event-related potential (ERP) at 1 second
erp_time = 1.0;  % seconds
erp_samples = round(erp_time * fs);
erp_duration = 0.5;  % seconds
erp_samples_duration = round(erp_duration * fs);

if erp_samples + erp_samples_duration <= nTime
    % Create ERP waveform (P300-like)
    erp_time_vec = (0:erp_samples_duration-1) / fs;
    erp_waveform = 15e-6 * exp(-erp_time_vec/0.1) .* sin(2*pi*3*erp_time_vec);
    
    % Add ERP to central and parietal channels
    central_channels = [5, 6, 7, 8];  % C3, C4, P3, P4
    for iChan = central_channels
        data(iChan, erp_samples:erp_samples+erp_samples_duration-1) = ...
            data(iChan, erp_samples:erp_samples+erp_samples_duration-1) + erp_waveform;
    end
end

%% ===== BASIC BUTTERFLY PLOT =====
fprintf('Creating basic butterfly plot...\n');

figure1 = visualize_eeg_meg_timeseries(data, time_vector, channel_names, ...
                                      'Title', 'EEG Time Series - Butterfly Mode');

%% ===== COLUMN DISPLAY =====
fprintf('Creating column display...\n');

figure2 = visualize_eeg_meg_timeseries(data, time_vector, channel_names, ...
                                      'DisplayMode', 'column', ...
                                      'Title', 'EEG Time Series - Column Mode', ...
                                      'ShowGrid', true);

%% ===== BUTTERFLY WITH GFP =====
fprintf('Creating butterfly plot with Global Field Power...\n');

figure3 = visualize_eeg_meg_timeseries(data, time_vector, channel_names, ...
                                      'DisplayMode', 'butterfly', ...
                                      'ShowGFP', true, ...
                                      'ShowLegend', true, ...
                                      'Title', 'EEG Time Series with GFP');

%% ===== GENERATE SAMPLE MEG DATA =====
fprintf('Generating sample MEG data...\n');

% MEG data is typically in Tesla (much smaller values)
meg_data = data * 1e-9;  % Convert to Tesla range (femtoTesla)

% MEG channel names
meg_channels = {'MEG001', 'MEG002', 'MEG003', 'MEG004', ...
                'MEG005', 'MEG006', 'MEG007', 'MEG008'};

%% ===== MEG VISUALIZATION =====
fprintf('Creating MEG visualization...\n');

figure4 = visualize_eeg_meg_timeseries(meg_data, time_vector, meg_channels, ...
                                      'Modality', 'MEG', ...
                                      'DisplayMode', 'butterfly', ...
                                      'Title', 'MEG Time Series', ...
                                      'ShowGFP', true);

%% ===== CUSTOM COLORS EXAMPLE =====
fprintf('Creating visualization with custom colors...\n');

% Define custom colors (one for each channel)
custom_colors = [
    1.0, 0.0, 0.0;  % Red
    0.0, 1.0, 0.0;  % Green
    0.0, 0.0, 1.0;  % Blue
    1.0, 1.0, 0.0;  % Yellow
    1.0, 0.0, 1.0;  % Magenta
    0.0, 1.0, 1.0;  % Cyan
    0.5, 0.5, 0.5;  % Gray
    1.0, 0.5, 0.0;  % Orange
];

figure5 = visualize_eeg_meg_timeseries(data, time_vector, channel_names, ...
                                      'DisplayMode', 'column', ...
                                      'LineColors', custom_colors, ...
                                      'Title', 'EEG with Custom Colors', ...
                                      'FlipYAxis', false);

%% ===== SEEG EXAMPLE =====
fprintf('Creating SEEG visualization example...\n');

% Generate SEEG-like data (higher amplitude, more localized)
seeg_data = data * 5;  % SEEG typically has higher amplitudes
seeg_channels = {'SEEG1', 'SEEG2', 'SEEG3', 'SEEG4', ...
                 'SEEG5', 'SEEG6', 'SEEG7', 'SEEG8'};

figure6 = visualize_eeg_meg_timeseries(seeg_data, time_vector, seeg_channels, ...
                                      'Modality', 'SEEG', ...
                                      'DisplayMode', 'column', ...
                                      'Title', 'SEEG Time Series', ...
                                      'ShowGrid', true, ...
                                      'FlipYAxis', true);

%% ===== SUMMARY =====
fprintf('\n=== VISUALIZATION SUMMARY ===\n');
fprintf('Created %d figures demonstrating different visualization modes:\n', 6);
fprintf('1. Basic EEG butterfly plot\n');
fprintf('2. EEG column display with grid\n');
fprintf('3. EEG butterfly with Global Field Power and legend\n');
fprintf('4. MEG butterfly plot with automatic unit scaling\n');
fprintf('5. EEG column display with custom colors\n');
fprintf('6. SEEG column display with flipped Y-axis\n');
fprintf('\nAll figures use Brainstorm-style visualization principles.\n');

%% ===== INTERACTIVE FEATURES NOTE =====
fprintf('\n=== INTERACTIVE FEATURES ===\n');
fprintf('Each figure stores metadata that could be used for:\n');
fprintf('- Zooming and panning\n');
fprintf('- Channel selection\n');
fprintf('- Time cursor display\n');
fprintf('- Amplitude scaling\n');
fprintf('- Export functionality\n');
fprintf('\nAccess stored data with: getappdata(figure_handle, ''TimeSeriesInfo'')\n');

% Example of accessing stored data
ts_info = getappdata(figure1, 'TimeSeriesInfo');
fprintf('\nExample - Figure 1 contains:\n');
fprintf('- %d channels\n', length(ts_info.channel_names));
fprintf('- %d time points\n', length(ts_info.time_vector));
fprintf('- Modality: %s\n', ts_info.modality);
fprintf('- Display mode: %s\n', ts_info.display_mode);
fprintf('- Units: %s\n', ts_info.units);
