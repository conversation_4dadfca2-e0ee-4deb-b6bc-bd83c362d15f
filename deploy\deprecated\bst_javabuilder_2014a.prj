<deployment-project plugin="plugin.ezdeploy" plugin-version="1.0">
  <configuration file="C:\Work\Dev\brainstorm3\deploy\bst_javabuilder_2014a.prj" location="C:\Work\Dev\brainstorm3_deploy\R2014a" name="bst_javabuilder" preferred-package-location="C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib" preferred-package-type="package.type.exe" target="target.ezdeploy.library" target-name="MATLAB Compiler for Libraries and Classes">
    <param.appname>bst_javabuilder</param.appname>
    <param.icon></param.icon>
    <param.icons></param.icons>
    <param.version>3.4</param.version>
    <param.authnamewatermark><PERSON><PERSON></param.authnamewatermark>
    <param.email><EMAIL></param.email>
    <param.company>McGill University</param.company>
    <param.summary />
    <param.description />
    <param.screenshot />
    <param.guid />
    <param.installpath.string>\McGill University\bst_javabuilder\</param.installpath.string>
    <param.installpath.combo>option.installpath.programfiles</param.installpath.combo>
    <param.logo />
    <param.install.notes />
    <param.intermediate>C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\src</param.intermediate>
    <param.output>C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib</param.output>
    <param.user.defined.mcr.options />
    <param.embed.ctf>true</param.embed.ctf>
    <param.server.ctf>C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\src\bst_javabuilder.ctf</param.server.ctf>
    <param.server.readme>C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\src\readme.txt</param.server.readme>
    <param.target.type>subtarget.java.package</param.target.type>
    <param.support.packages />
    <param.namespace>bst_javabuilder</param.namespace>
    <param.classorg />
    <param.function.data />
    <param.web.mcr>true</param.web.mcr>
    <param.package.mcr>false</param.package.mcr>
    <param.no.mcr>false</param.no.mcr>
    <param.web.mcr.name>BrainstormInstaller</param.web.mcr.name>
    <param.package.mcr.name>MyAppInstaller_mcr</param.package.mcr.name>
    <param.no.mcr.name>MyAppInstaller_app</param.no.mcr.name>
    <param.windows.command.prompt>true</param.windows.command.prompt>
    <param.create.log>false</param.create.log>
    <param.log.file />
    <param.user.only.registration>false</param.user.only.registration>
    <param.net.framework>option.net.framework.default</param.net.framework>
    <param.assembly.type>false</param.assembly.type>
    <param.encryption.key.file />
    <param.net.enable.remoting>false</param.net.enable.remoting>
    <param.net.tsa.enable>false</param.net.tsa.enable>
    <param.net.tsa.assembly />
    <param.net.tsa.interface />
    <param.net.tsa.namespace>bst_javabuilder</param.net.tsa.namespace>
    <param.net.tsa.superclass>Run</param.net.tsa.superclass>
    <param.net.tsa.metadata />
    <param.net.tsa.metadata.assembly>C:\Work\Dev\brainstorm3</param.net.tsa.metadata.assembly>
    <unset>
      <param.summary />
      <param.description />
      <param.screenshot />
      <param.guid />
      <param.installpath.string />
      <param.installpath.combo />
      <param.logo />
      <param.install.notes />
      <param.intermediate />
      <param.output />
      <param.user.defined.mcr.options />
      <param.embed.ctf />
      <param.server.ctf />
      <param.server.readme />
      <param.support.packages />
      <param.namespace />
      <param.classorg />
      <param.function.data />
      <param.web.mcr />
      <param.package.mcr />
      <param.no.mcr />
      <param.package.mcr.name />
      <param.no.mcr.name />
      <param.windows.command.prompt />
      <param.create.log />
      <param.log.file />
      <param.user.only.registration />
      <param.net.framework />
      <param.assembly.type />
      <param.encryption.key.file />
      <param.net.enable.remoting />
      <param.net.tsa.enable />
      <param.net.tsa.assembly />
      <param.net.tsa.interface />
      <param.net.tsa.namespace />
      <param.net.tsa.superclass />
      <param.net.tsa.metadata />
      <param.net.tsa.metadata.assembly />
    </unset>
    <fileset.exports>
      <file>C:\Work\Dev\brainstorm3\brainstorm.m</file>
    </fileset.exports>
    <fileset.classes>
      <entity.package name="">
        <entity.class name="Run">
          <file>C:\Work\Dev\brainstorm3\brainstorm.m</file>
        </entity.class>
      </entity.package>
    </fileset.classes>
    <fileset.resources>
      <file>C:\Work\Dev\brainstorm3\defaults</file>
      <file>C:\Work\Dev\brainstorm3\deploy</file>
      <file>C:\Work\Dev\brainstorm3\doc</file>
      <file>C:\Work\Dev\brainstorm3\external</file>
      <file>C:\Work\Dev\brainstorm3\java</file>
      <file>C:\Work\Dev\brainstorm3\toolbox</file>
      <file>C:\Work\Dev\brainstorm3_deploy\R2014a\sigproc</file>
    </fileset.resources>
	<fileset.package />
    <build-deliverables>
      <file name="readme.txt" location="C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib" optional="true">C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib\readme.txt</file>
      <file name="bst_javabuilder.jar" location="C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib" optional="false">C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib\bst_javabuilder.jar</file>
      <file name="doc" location="C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib" optional="false">C:\Work\Dev\brainstorm3_deploy\R2014a\bst_javabuilder\distrib\doc</file>
    </build-deliverables>
    <workflow />
    <matlab>
      <root>C:\Program Files\MATLAB\R2014a</root>
      <toolboxes>
        <toolbox name="matlabcoder" />
      </toolboxes>
      <toolbox>
        <matlabcoder>
          <enabled>true</enabled>
        </matlabcoder>
      </toolbox>
      <path>
        <directory>C:\Work\Dev\brainstorm3\toolbox</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\anatomy</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\connectivity</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\core</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\db</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\forward</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\gui</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\inverse</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\io</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\math</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\misc</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\process</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\process\functions</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\realtime</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\script</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\sensors</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\timefreq</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\tree</directory>
        <directory>C:\Work\Dev\brainstorm3\external</directory>
        <directory>C:\Work\Dev\brainstorm3\external\ReadWriteLena</directory>
        <directory>C:\Work\Dev\brainstorm3\external\ReadWriteLena\Input</directory>
        <directory>C:\Work\Dev\brainstorm3\external\ReadWriteLena\Output</directory>
		<directory>C:\Work\Dev\brainstorm3\external\ScreenCapture</directory>
	    <directory>C:\Work\Dev\brainstorm3\external\brainentropy</directory>
		<directory>C:\Work\Dev\brainstorm3\external\dba</directory>
        <directory>C:\Work\Dev\brainstorm3\external\eeprobe</directory>
        <directory>C:\Work\Dev\brainstorm3\external\fieldtrip</directory>
        <directory>C:\Work\Dev\brainstorm3\external\freesurfer</directory>
        <directory>C:\Work\Dev\brainstorm3\external\icp</directory>
        <directory>C:\Work\Dev\brainstorm3\external\iso2mesh</directory>
        <directory>C:\Work\Dev\brainstorm3\external\label</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mne</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mne\matlab</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mominc</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mosher</directory>
        <directory>C:\Work\Dev\brainstorm3\external\octave</directory>
        <directory>C:\Work\Dev\brainstorm3\external\openmeeg</directory>
        <directory>C:\Work\Dev\brainstorm3\external\other</directory>
        <directory>C:\Work\Dev\brainstorm3\external\yokogawa</directory>
        <directory>C:\Work\Dev\brainstorm3</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\demos</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\graph2d</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\graph3d</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\graphics</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\plottools</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\scribe</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\specgraph</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\uitools</directory>
        <directory>${MATLAB_ROOT}\toolbox\local</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\optimfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\codetools</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\datafun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\datamanager</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\datatypes</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\elfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\elmat</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\funfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\general</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\guide</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\helptools</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\iofun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\lang</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\matfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\ops</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\polyfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\randfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\sparfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\specfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\strfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\timefun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\verctrl</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\winfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\winfun\NET</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\apps</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\audiovideo</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\comparisons</directory>
        <directory>${MATLAB_ROOT}\toolbox\compiler</directory>
        <directory>${MATLAB_ROOT}\toolbox\compiler\compilerdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\distcomp</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\user</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\mpi</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\parallel</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\parallel\util</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\lang</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\cluster</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\gpu</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\array</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\pctdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\imagesci</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\instrument</directory>
        <directory>${MATLAB_ROOT}\toolbox\javabuilder\javabuilder</directory>
        <directory>${MATLAB_ROOT}\toolbox\javabuilder\javabuilderdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\asynciolib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\coder\coder</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\general</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\graphics</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\graphics\utils</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\graphics\plotoptions</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\dastudio</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\filterdesignlib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\measure</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\multimedia</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\rptgen</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\siglib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\spcuilib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\testmeaslib\general</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\testmeaslib\graphics</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\signal</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\sigtools</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\sptoolgui</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\sigdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\simulink</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\system</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\timeseries</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\hds</directory>
      </path>
    </matlab>
    <platform>
      <unix>false</unix>
      <mac>false</mac>
      <windows>true</windows>
      <win2k>false</win2k>
      <winxp>false</winxp>
      <vista>false</vista>
      <linux>false</linux>
      <solaris>false</solaris>
      <osver>6.1</osver>
      <os32>false</os32>
      <os64>true</os64>
      <arch>win64</arch>
      <matlab>true</matlab>
    </platform>
  </configuration>
</deployment-project>