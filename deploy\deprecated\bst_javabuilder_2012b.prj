<?xml version="1.0" encoding="UTF-8"?>
<deployment-project plugin="plugin.deploytool" plugin-version="1.0">
  <configuration target="target.java.package" target-name="Java Package" name="bst_javabuilder" location="C:\Work\Dev\brainstorm3_deploy\R2012b" file="C:\Work\Dev\brainstorm3\deploy\bst_javabuilder_2012b.prj">
    <param.project.name>C:\Work\Dev\brainstorm3\deploy\bst_javabuilder_2012b.prj</param.project.name>
    <param.target.type>Java Package</param.target.type>
    <param.appname>bst_javabuilder</param.appname>
    <param.intermediate>C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\src</param.intermediate>
    <param.output>C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib</param.output>
    <param.embed.ctf>true</param.embed.ctf>
    <param.tbx_on_path>
      <item>all</item>
    </param.tbx_on_path>
    <param.warnings>
      <warning.fullpath>on</warning.fullpath>
      <warning.duplicate>on</warning.duplicate>
      <warning.option.ignored>on</warning.option.ignored>
      <warning.libname>on</warning.libname>
      <warning.demo>on</warning.demo>
    </param.warnings>
    <param.share.mcr>false</param.share.mcr>
    <unset>
      <param.project.name />
      <param.target.type />
      <param.appname />
      <param.intermediate />
      <param.output />
      <param.embed.ctf />
      <param.tbx_on_path />
      <param.warnings />
      <param.share.mcr />
    </unset>
    <fileset.classes>
      <entity.package name="">
        <entity.class name="Run">
          <file>C:\Work\Dev\brainstorm3\brainstorm.m</file>
        </entity.class>
      </entity.package>
    </fileset.classes>
    <fileset.resources>
      <file>C:\Work\Dev\brainstorm3\defaults</file>
      <file>C:\Work\Dev\brainstorm3\deploy</file>
      <file>C:\Work\Dev\brainstorm3\doc</file>
      <file>C:\Work\Dev\brainstorm3\external</file>
      <file>C:\Work\Dev\brainstorm3\java</file>
      <file>C:\Work\Dev\brainstorm3\toolbox</file>
      <file>C:\Work\Dev\brainstorm3_deploy\R2012b\sigproc</file>
    </fileset.resources>
    <fileset.package />
    <build-deliverables>
      <file name="doc" location="C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib" optional="false">C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib\doc</file>
      <file name="bst_javabuilder.jar" location="C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib" optional="false">C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib\bst_javabuilder.jar</file>
      <file name="readme.txt" location="C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib" optional="true">C:\Work\Dev\brainstorm3_deploy\R2012b\bst_javabuilder\distrib\readme.txt</file>
    </build-deliverables>
    <workflow />
    <matlab>
      <root>C:\Program Files\MATLAB\R2012b</root>
      <path>
        <directory>C:\Work\Dev\brainstorm3\toolbox</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\anatomy</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\connectivity</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\core</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\db</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\forward</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\gui</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\inverse</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\io</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\math</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\misc</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\process</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\process\functions</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\realtime</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\script</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\sensors</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\timefreq</directory>
        <directory>C:\Work\Dev\brainstorm3\toolbox\tree</directory>
        <directory>C:\Work\Dev\brainstorm3\external</directory>
        <directory>C:\Work\Dev\brainstorm3\external\ReadWriteLena</directory>
        <directory>C:\Work\Dev\brainstorm3\external\ReadWriteLena\Input</directory>
        <directory>C:\Work\Dev\brainstorm3\external\ReadWriteLena\Output</directory>
		<directory>C:\Work\Dev\brainstorm3\external\ScreenCapture</directory>
	    <directory>C:\Work\Dev\brainstorm3\external\brainentropy</directory>
		<directory>C:\Work\Dev\brainstorm3\external\dba</directory>
        <directory>C:\Work\Dev\brainstorm3\external\eeprobe</directory>
        <directory>C:\Work\Dev\brainstorm3\external\fieldtrip</directory>
        <directory>C:\Work\Dev\brainstorm3\external\freesurfer</directory>
        <directory>C:\Work\Dev\brainstorm3\external\icp</directory>
        <directory>C:\Work\Dev\brainstorm3\external\iso2mesh</directory>
        <directory>C:\Work\Dev\brainstorm3\external\label</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mne</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mne\matlab</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mominc</directory>
        <directory>C:\Work\Dev\brainstorm3\external\mosher</directory>
        <directory>C:\Work\Dev\brainstorm3\external\octave</directory>
        <directory>C:\Work\Dev\brainstorm3\external\openmeeg</directory>
        <directory>C:\Work\Dev\brainstorm3\external\other</directory>
        <directory>C:\Work\Dev\brainstorm3\external\yokogawa</directory>
        <directory>C:\Work\Dev\brainstorm3</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\demos</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\graph2d</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\graph3d</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\graphics</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\plottools</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\scribe</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\specgraph</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\uitools</directory>
        <directory>${MATLAB_ROOT}\toolbox\local</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\optimfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\codetools</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\datafun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\datamanager</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\datatypes</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\elfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\elmat</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\funfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\general</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\guide</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\helptools</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\iofun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\lang</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\matfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\ops</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\polyfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\randfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\sparfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\specfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\strfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\timefun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\verctrl</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\winfun</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\winfun\NET</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\apps</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\audiovideo</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\comparisons</directory>
        <directory>${MATLAB_ROOT}\toolbox\compiler</directory>
        <directory>${MATLAB_ROOT}\toolbox\compiler\compilerdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\distcomp</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\user</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\mpi</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\parallel</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\parallel\util</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\lang</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\cluster</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\gpu</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\array</directory>
        <directory>${MATLAB_ROOT}\toolbox\distcomp\pctdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\imagesci</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\instrument</directory>
        <directory>${MATLAB_ROOT}\toolbox\javabuilder\javabuilder</directory>
        <directory>${MATLAB_ROOT}\toolbox\javabuilder\javabuilderdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\sam\m3i</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\asynciolib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\coder\coder</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\general</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\graphics</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\graphics\utils</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\controllib\graphics\plotoptions</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\dastudio</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\filterdesignlib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\measure</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\multimedia</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\rptgen</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\siglib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\spcuilib</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\testmeaslib\general</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\testmeaslib\graphics</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\signal</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\sigtools</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\sptoolgui</directory>
        <directory>${MATLAB_ROOT}\toolbox\signal\sigdemos</directory>
        <directory>${MATLAB_ROOT}\toolbox\shared\simulink</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\system</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\timeseries</directory>
        <directory>${MATLAB_ROOT}\toolbox\matlab\hds</directory>
      </path>
    </matlab>
    <platform>
      <unix>false</unix>
      <mac>false</mac>
      <windows>true</windows>
      <win2k>false</win2k>
      <winxp>false</winxp>
      <vista>false</vista>
      <linux>false</linux>
      <solaris>false</solaris>
      <osver>7</osver>
      <os32>false</os32>
      <os64>true</os64>
      <arch>win64</arch>
      <matlab>true</matlab>
    </platform>
  </configuration>
</deployment-project>

