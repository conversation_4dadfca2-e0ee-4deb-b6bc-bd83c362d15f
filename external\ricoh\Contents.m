% RICOH MEG Reader toolbox for MATLAB
% Version 1.0.3 13-Sep-2018
%
% Files
%   getRData          - Get measured data
%   getRHdrSystem     - Get header of the system information
%   getRHdrChannel    - Get header of the system information
%   getRHdrAcqCond    - Get header of the system information
%   getRHdrEvent      - Get header of the trigger event
%   getRHdrCoregist   - Get header of the system information
%   getRHdrDigitize   - Get header of the digitization information
%   getRHdrSubject    - Get header of the subject information
%   getRHdrAnnotation - Get header of annotation
%   getRHdrSource     - Get header of the source information
%   getRMriHdr        - Get header of mri
%   getRVersion       - Get toolbox version
%
%------------------------------------------------------------
%                          History
%------------------------------------------------------------
% R1.0.3 : 2018.09.13 - support Axial2ndOrderGradiometer
% R1.0.2 : 2018.06.04 - modify comment(fear of misinterpretation)
% R1.0.1 : 2018.03.14 - modify conflict 3rd party file 
% R1.0.0 : 2017.09.27 - First release
% 
%-----------------------------------------------------------------------
% Copyright (C) 2017-2018     Ricoh Company Ltd. All Rights Reserved.
% All rights reserved.