function [ template ] = db_template(structureName)
% DB_TEMPLATE: Defines a template structure for all the Brainstorm data types.
%
% USAGE :  [template] = db_template(structureName);

% @=============================================================================
% This function is part of the Brainstorm software:
% https://neuroimage.usc.edu/brainstorm
% 
% Copyright (c) University of Southern California & McGill University
% This software is distributed under the terms of the GNU General Public License
% as published by the Free Software Foundation. Further details on the GPLv3
% license can be found at http://www.gnu.org/copyleft/gpl.html.
% 
% FOR RESEARCH PURPOSES ONLY. THE SOFTWARE IS PROVIDED "AS IS," AND THE
% UNIVERSITY OF SOUTHERN CALIFORNIA AND ITS COLLABORATORS DO NOT MAKE ANY
% WARRANTY, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF
% MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE, NOR DO THEY ASSUME ANY
% LIABILITY OR RESPONSIBILITY FOR THE USE OF THIS SOFTWARE.
%
% For more information type "brainstorm license" at command prompt.
% =============================================================================@
%
% Authors: <AUTHORS>


switch lower(structureName)
    % ===== BASIC LOADED STRUCTURES =====
    case 'anatomy'
        template = struct('Comment',  '', ...
                          'FileName', '');
    case 'surface'
        template = struct('Comment',     '', ...
                          'FileName',    '', ...
                          'SurfaceType', '');
    case 'channel'
        template = struct('FileName',        '', ...
                          'Comment',         '', ...
                          'nbChannels',      0, ...
                          'Modalities',      [], ...
                          'DisplayableSensorTypes', []);
    case 'data'
        template = struct('FileName',       '', ...
                          'Comment',        '', ...
                          'DataType',       'recordings', ...
                          'BadTrial',       0);
    case 'dipoles'
        template = struct('FileName',       '', ...
                          'Comment',        '', ...
                          'DataFile',       '');
    case 'headmodel'
        template = struct('FileName',       '', ...
                          'HeadModelType',  '', ...
                          'MEGMethod',      '', ...
                          'EEGMethod',      '', ...
                          'Comment',        '', ...
                          'ECOGMethod',     '', ...
                          'SEEGMethod',     '');
    case 'results'
        template = struct('FileName',       '', ...
                          'Comment',        '', ...
                          'DataFile',       '', ...
                          'isLink',         0, ...
                          'HeadModelType',  '');
    case 'stat'
        template = struct('FileName',       '', ...
                          'Comment',        '', ...
                          'Type',           '', ...
                          'pThreshold',     '', ...
                          'DataFile',       '');
    case 'timefreq'
        template = struct('FileName',       '', ...
                          'Comment',        '', ...
                          'DataFile',       '', ...
                          'DataType',       '');
    case 'image'
        template = struct('FileName',       '', ...
                          'Comment',        '');
    case 'matrix'
        template = struct('FileName',       '', ...
                          'Comment',        '');
    case {'noisecov', 'ndatacov'}
        template = struct('FileName',       '', ...
                          'Comment',        '');
        
    % ==== FILES ====
    case 'mrimat'
        template = struct(...
            'Comment',     '', ...
            'Cube',        [], ...
            'Voxsize',     [], ...
            'NCS',         [], ...
            'SCS',         [], ...
            'Header',      [], ...
            'Histogram',   [], ...
            'InitTransf',  [], ...   % Cell-array Nx2: {label, transf4x4}, label={'vox2ras', 'permute', 'flipdim', 'reg'}
            'Labels',      [], ...
            'History',     []);

    case 'surfacemat'
        template = struct(...
            'Comment',         '', ...
            'Vertices',        [], ...
            'Faces',           [], ...
            'Color',           [], ...
            'VertConn',        [], ...
            'VertNormals',     [], ...
            'Curvature',       [], ...
            'SulciMap',        [], ...
            'Atlas',           db_template('Atlas'), ...
            'iAtlas',          1, ...
            'tess2mri_interp', [], ...
            'Reg',             [], ...
            'History',         []);

    case 'fibersmat'
        template = struct(...
            'Comment',  [], ...
            'Header',   [], ...
            'Points',   [], ...
            'Colors',   [], ...
            'Scouts', struct(...
                'ConnectFile', [], ...
                'Assignment',  []), ...
            'History',  []);
        
    case 'femmat'                        % FEM head model (tetrahedral or hexahedral mesh)
        template = struct(...
            'Comment',         '', ...
            'Vertices',        [], ...   % [Nvert x 3] double: List of position of the nodes with their three cartesian coordinates
            'Elements',        [], ...   % [Nelem x 4] integers for tetrahedral meshes; or [Nelem x 8] integers for hexahedral meshes (1-based indices in the Vertices matrix)
            'Tissue',          [], ...   % [Nelem x 1] integer: tissue classification for each node
            'TissueLabels',    [], ...   % [1 x Ntissue] cell array: label of the tissues in this head model
            'Tensors',         [], ...   % [Nelem x 12] double: Conductivity tensors: [V1(1:3) V2(1:3) V3(1:3) L1 L2 L3]
            'History',         []);
        
    case 'datamat'
        template = struct('F',            [], ...
                          'Std',          [], ...
                          'Comment',      '', ...
                          'ChannelFlag',  [], ...
                          'Time',         [], ...
                          'DataType',     'recordings', ...
                          'Device',       '', ...
                          'nAvg',         1, ...
                          'Leff',         1, ...
                          'Events',       repmat(db_template('event'), 0), ...
                          'ColormapType', [], ...
                          'DisplayUnits', [], ...
                          'History',      []);
    case 'noisecovmat'
        template = struct('NoiseCov',     [], ...
                          'Comment',      '', ...
                          'nSamples',     [], ...
                          'FourthMoment', [], ...
                          'History',      []);
    case 'headmodelmat'
        template = struct('MEGMethod',     [], ...
                          'EEGMethod',     [], ...
                          'ECOGMethod',    [], ...
                          'SEEGMethod',    [], ...
                          'Gain',          [], ... 
                          'Comment',       '', ...  [nChannel, 3*nSources]
                          'HeadModelType', '', ...  {'volume', 'surface'}
                          'GridLoc',       [], ...  [nSources, 3]
                          'GridOrient',    [], ...  [nSources, 3]
                          'GridAtlas',     [], ...
                          'GridOptions',   [], ...
                          'SurfaceFile',   '', ...
                          'Param',         []);
    case 'resultsmat'
        template = struct('ImagingKernel', [], ...
                          'ImageGridAmp',  [], ...
                          'Std',           [], ...
                          'Whitener',      [], ...
                          'SourceDecompSa',[], ...
                          'SourceDecompVa',[], ...
                          'nComponents',   1, ...
                          'Comment',       '', ...
                          'Function',      '', ...
                          'Time',          [], ...
                          'DataFile',      '', ...
                          'HeadModelFile', '', ...
                          'HeadModelType', 'surface', ...
                          'ChannelFlag',   [], ...
                          'GoodChannel',   [], ...
                          'SurfaceFile',   [], ...
                          'Atlas',         [], ...
                          'GridLoc',       [], ...
                          'GridOrient',    [], ...
                          'GridAtlas',     [], ...
                          'Options',       [], ...
                          'ColormapType',  [], ...
                          'DisplayUnits',  [], ...
                          'ZScore',        [], ...
                          'nAvg',          [], ...
                          'Leff',          [], ...
                          'History',       []);
    case 'timefreqmat'
        template = struct('TF',          [], ...
                          'TFmask',      [], ...
                          'Std',         [], ...
                          'Comment',     '', ...
                          'DataType',    '', ...
                          'Time',        [], ...
                          'TimeBands',   [], ...
                          'Freqs',       [], ...
                          'RefRowNames', [], ...
                          'RowNames',    [], ...
                          'Measure',     [], ...
                          'Method',      [], ...
                          'DataFile',    '', ...
                          'SurfaceFile', [], ...
                          'GridLoc',     [], ...
                          'GridAtlas',   [], ...
                          'Atlas',       [], ...
                          'HeadModelFile', [], ...
                          'HeadModelType', [], ...
                          'nAvg',        1, ...
                          'Leff',        1, ...
                          'ColormapType',[], ...
                          'DisplayUnits',[], ...
                          'Options',     [], ...
                          'History',     []);
    case 'channelmat'
        template = struct(...
            'Comment',    'Channels', ...
            'MegRefCoef', [], ...   % CTF compensators matrix, [nMeg x nMegRef]
            'Projector',  [], ...   % SSP matrix, [nChannels x nChannels]
            'TransfMeg',  [], ...   % MEG sensors: Successive transforms from device coord. system to brainstorm SCS
            'TransfMegLabels', [], ... % Labels for each MEG transformation
            'TransfEeg',  [], ...   % EEG sensors: Successive transforms from device coord. system to brainstorm SCS
            'TransfEegLabels', [], ... % Labels for each EEG transformation
            'HeadPoints', struct(...% Digitized head points 
                'Loc',    [], ...
                'Label',  [], ...
                'Type',   []), ...
            'Channel',    [], ...  % [nChannels] Structure array, one structure per sensor
            'Clusters',   [], ...
            'IntraElectrodes', [], ...
            'History',     []);

    case 'dipolemat'
        template = struct(...
            'Comment',     '', ...
            'Time',        [], ...
            'DipoleNames', [], ...
            'Subset',      [], ...
            'Dipole',      repmat(struct(...
                'Index',         0, ...
                'Time',          0, ...
                'Origin',        [0 0 0], ...
                'Loc',           [0 0 0], ...
                'Amplitude',     [0 0 0], ...
                'Goodness',      [], ...
                'Errors',        0, ...
                'Noise',         0, ...
                'SingleError',   [0 0 0 0 0], ...
                'ErrorMatrix',   zeros(1,25), ...
                'ConfVol',       [], ...
                'Khi2',          [], ...
                'DOF',           [], ...
                'Probability',   0, ...
                'NoiseEstimate', 0, ...
                'Perform',       0), 0), ...
            'DataFile', '', ...
            'Options',  [], ...
            'History',  []);
        
    case 'projector'
        template = struct(...
            'Comment',      [], ...
            'Components',   [], ...
            'CompMask',     [], ...
            'Status',       0, ...  % 0: not applied; 1: applied on the fly; 2: saved in the file, not revertible : ADDITIONAL VALUES = EEG REFERENCES
            'SingVal',      [], ...
            'Method',       '');
        
    case 'matrixmat'
        template = struct(...
            'Value',       [], ...
            'Std',         [], ...
            'Comment',     '', ...
            'Description', [], ...  % String to describe each row of the Value array, can be 'scout-name.vertex.component' e.g. 'ACC_pre L.2068.1'
            'Time',        [], ...
            'ChannelFlag', [], ...
            'nAvg',        1, ...
            'Leff',        1, ...
            'Events',      repmat(db_template('event'), 0), ...
            'SurfaceFile', [], ...
            'Atlas',       [], ...
            'DisplayUnits',[], ...
            'History',     []);
    case 'statmat'
        template = struct(...
            'pmap',          [], ...
            'tmap',          [], ...
            'df',            0, ...
            'SPM',           [], ...
            'Correction',    'no', ...
            'Type',          '', ...
            'Comment',       '', ...
            'Time',          [], ...
            'ChannelFlag',   [], ...
            ... % Results fields
            'HeadModelType', [], ...
            'SurfaceFile',   [], ...
            'nComponents',   [], ...
            'Atlas',         [], ...
            'GridLoc',       [], ...
            'GridOrient',    [], ...
            'GridAtlas',     [], ...
            'GoodChannel',   [], ...
            'ColormapType',  '', ...
            'DisplayUnits',  [], ...
            'History',       [], ...
            ... % Matrix fields
            'Description',   [], ...  
            ... % Timefreq fields
            'TFmask',        [], ...
            'DataType',      [], ...
            'Freqs',         [], ...
            'Method',        [], ...
            'Options',       [], ...
            'RefRowNames',   [], ...
            'RowNames',      [], ...
            'TimeBands',     [], ...
            'Measure',       [], ...
            'StatClusters',  []);
            
    % ==== SUBJECT ====
    case 'subjectmat'
        template = struct(...
            'Comments',    '', ...
            'Anatomy',     '', ...
            'Cortex',      '', ...
            'Scalp',       '', ...
            'InnerSkull',  '', ...
            'OuterSkull',  '', ...
            'Fibers',      '', ...
            'FEM',         '', ...
            'UseDefaultAnat',    0, ...
            'UseDefaultChannel', 1); 
    case 'subject'
        template = struct(...
              'Name',                'Unnamed', ...
              'Comments',            '', ...
              'FileName',            '', ...
              'DateOfAcquisition',   '', ...
              'Anatomy',             repmat(db_template('Anatomy'),0), ...
              'Surface',             repmat(db_template('Surface'),0), ...
              'iAnatomy',            [], ...
              'iScalp',              [], ...
              'iCortex',             [], ...
              'iInnerSkull',         [], ...
              'iOuterSkull',         [], ...
              'iFibers',             [], ...
              'iFEM',                [], ...
              'iOther',              [], ...
              'UseDefaultAnat',      0 , ... 
              'UseDefaultChannel',   1);
  
    % ==== STUDY ====
    case 'studymat'
        template = struct(...
              'DateOfStudy', date, ...
              'Name',        'Unnamed', ...
              'BadTrials',   []);
    case 'study'
         template = struct(...
              'Name',                'Unnamed', ...
              'FileName',            '', ...
              'DateOfStudy',         '', ...
              'BrainStormSubject',   '', ...
              'Condition',           '', ...
              ... repmat(db_template('Channel'),0)
              'Channel',             struct(...  
                  'FileName',        {}, ...
                  'Comment',         {}, ...
                  'nbChannels',      {}, ...
                  'Modalities' ,     {}, ...
                  'DisplayableSensorTypes', {}), ...
              'iChannel',            [], ...
              ... repmat(db_template('Data'),0)
              'Data',              struct(...
                  'FileName',        {}, ...
                  'Comment',         {}, ...
                  'DataType',        {}, ...
                  'BadTrial',        {}), ...
              ... repmat(db_template('HeadModel'),0)
              'HeadModel',         struct(...
                  'FileName',         {}, ...
                  'HeadModelType',    {}, ...
                  'MEGMethod',        {}, ...
                  'EEGMethod',        {}, ...
                  'Comment',          {}, ...
                  'ECOGMethod',       {}, ...
                  'SEEGMethod',       {}), ...
              'iHeadModel',        [], ...
              ... repmat(db_template('Results'),0)
              'Result',              struct(...
                  'FileName',        {}, ...
                  'Comment',         {}, ...
                  'DataFile',        {}, ...
                  'isLink',          {}, ...
                  'HeadModelType',   {}), ...
              ... repmat(db_template('Stat'),  0)
              'Stat',                struct(...
                  'FileName',        {}, ...
                  'Comment',         {}, ...
                  'Type',            {}, ...
                  'pThreshold',      {}, ...
                  'DataFile',        {}), ...
              ... repmat(db_template('Image'),  0)
              'Image',               struct(...
                  'FileName',        {}, ...
                  'Comment',         {}), ...
              ... repmat(db_template('noiseCov'), 0)
              'NoiseCov',            struct(...
                  'FileName',        {}, ...
                  'Comment',         {}), ...
              ... repmat(db_template('Dipoles'), 0)
              'Dipoles',             struct(...
                  'FileName',        {}, ...
                  'Comment',         {}, ...
                  'DataFile',        {}), ...
              ... repmat(db_template('Timefreq'),  0)
              'Timefreq',            struct(...
                  'FileName',        {}, ...
                  'Comment',         {}, ...
                  'DataFile',        {}, ...
                  'DataType',        {}), ...
              ... repmat(db_template('Matrix'),  0)
              'Matrix',             struct(...
                  'FileName',        {}, ...
                  'Comment',         {}));
          
    % ==== PROTOCOLINFO ====
    case 'protocolinfo'
        template = struct(...
              'Comment',               'default_protocol', ...
              'STUDIES',               '', ...
              'SUBJECTS',              '', ...
              'iStudy',                [], ...
              'UseDefaultAnat',        0, ...
              'UseDefaultChannel',     1);     

    case 'protocolsubjects' 
        template = struct(...
            'Subject',        repmat(db_template('Subject'), 0), ...
            'DefaultSubject', []);
        
    case 'protocolstudies'
        template = struct(...
            'Study',         repmat(db_template('Study'), 0), ...
            'DefaultStudy',  db_template('Study'), ...
            'AnalysisStudy', db_template('Study'));
        
    % ==== LAYOUT ====
    case 'layout'
        template = struct(...
            'MainWindowPos',   [0 0 0 0], ...
            'ExplorationMode', 'Subjects', ...     % {'Subjects', 'StudiesSubj', 'StudiesCond'}
            'PreviousExplorationMode', [], ...
            'WindowManager',   'TileWindows', ...  % {'WeightWindows', 'TileWindows', 'FullArea', 'FullScreen', 'None'}
            'UserSetups',      [], ...
            'DoubleScreen',    1, ...
            'FullScreen',      0);
        
    % ==== EVENT ====
    case 'event'
        % See: https://neuroimage.usc.edu/brainstorm/Tutorials/EventMarkers#On_the_hard_drive
        template = struct(...
            'label',      '', ...      % str, label of the event group. Should not be empty.
            'color',      [], ...      % array of double (R,G,B): color triplet, size: (1, 3). Values btwn 0 and 1. Cannot be empty.
            'epochs',     [], ...      % empty, or array of int (epochs indices), size: (1, nb of event items).
            'times',      [], ...      % array of double (time values), size: (1 or 2, nb of event items). Cannot be empty.
            'reactTimes', [], ...      % empty, or array of double (reaction times), size: (1, nb of event items).
            'select',     1, ...       % int: display flag (0 or 1).
            'channels',   [], ...      % empty, or cell array of cell arrays of str, size: (1, nb of event items:(1, nb of associated channels))
            'notes',      []);         % empty, cell array of str, size: (1, nb of event items)
        
    % ==== EPOCH ====
    case 'epoch'
        template = struct(...
                'label',   '', ...        
                'times',   [], ...        % [tStart, tStop]
                'nAvg',    1, ...         % Number of epochs averaged to produce this block
                'select',  1, ...
                'bad',     0, ...
                'channelflag', []);
        
    % ==== FILE IMPORT ====
    case 'sfile'
        template = struct(...
            'filename',   '', ...  % Full path to the file
            'format',     '', ...  % {fif,ctf,egi,neuroscan,brainamp,lena,cartool,...}
            'device',     '', ...  % {neuromag,ctf,egi,neuroscan,brainamp,...}
            'condition',  '', ...
            'comment',    '', ...
            'byteorder',  '', ...  % ieee-le, ieee-be...
            'prop', struct(...            % Properties of the recordings
                'times',        [], ...   % [tStart, tStop]
                'sfreq',        0, ...    % Sampling frequency
                'nAvg',         1, ...    % Number of trials used to create this file 
                'currCtfComp',  [], ...   % {0,1,3} Current CTF compensation order already applied to the recordings
                'destCtfComp',  []), ...  % {0,1,3} Destination CTF compensation order
            'epochs',      repmat(db_template('epoch'), 0), ...
            'events',      repmat(db_template('event'), 0), ...
            'header',      [], ...
            'channelflag', [], ...
            'acq_date',    []);
        
    case 'importoptions'
        template = struct(...
            'ImportMode',       'Epoch', ...           % Import mode:  {Epoch, Time, Event}
            'UseEvents',        0, ...                 % {0,1}: If 1, perform epoching around the selected events
            'TimeRange',        [], ...                % Specifying a time window for 'Time' import mode
            'EventsTimeRange',  [-0.1000 0.3000], ...  % Time range for epoching, zero is the event onset (if epoching is enabled)
            'GetAllEpochs',     0, ...                 % {0,1}: Import all arrays, no matter how many they are
            'iEpochs',          1, ...                 % Array of indices of epochs to import (if GetAllEpochs is not enabled)
            'SplitRaw',         0, ...                 % {0,1}: If 1, and if importing continuous recordings (no epoching, no events): split recordings in small time blocks
            'SplitLength',      2, ...                 % Duration of each split time block, in seconds
            'Resample',         0, ...                 % Enable resampling (requires Signal Processing Toolbox)
            'ResampleFreq',     0, ...                 % Resampling frequency (if resampling is enabled)
            'UseCtfComp',       1, ...                 % Get and apply CTF 3rd gradient correction if available 
            'UseSsp',           1, ...                 % Get and apply SSP (Signal Space Projection) vectors if available
            'RemoveBaseline',   'no', ...              % Method used to remove baseline of each channel: {no, all, time, sample}
            'BaselineRange',    [], ...                % [tStart,tStop] If RemoveBaseline is 'time'; Else ignored
            'BaselineSensorType', '', ...              % Sensor types (or names) to for baseline correction if RemoveBaseline is {all, time}
            'events',           [], ...                % Events structure: (label, epochs, samples, times, reactTimes, select)
            'CreateConditions', 0, ...                 % {0,1} If 1, create new conditions in Brainstorm database if it is more convenient
            'ChannelReplace',   1, ...        % If 1, prompts for automatic replacement of an existing channel file. If 2, replace it automatically. If 0, do not do it.
            'ChannelAlign',     1, ...        % If 1, prompts for automatic registration. If 2, perform it automatically. If 0, do not do it.
            'IgnoreShortEpochs',1, ...        % If 1, prompts for ignoring the epochs that are shorter that the others. If 2, ignore them automatically. If 0, do not do it.
            'EventsMode',       'ask', ...    % Where to get the events from: {'ask', 'ignore', Filename, ChannelName, ChannelNames}
            'EventsTrackMode',  'ask', ...    % {'value','bit','ttl','rttl','ignore','ask'} 
            'EventsTypes',      '', ...       % String with a list of eventtypes to use to group the epochs (EEGLAB only)
            'DisplayMessages',  1, ...        % If 0, do not show any of the message boxes that the user would normally see
            'Precision',        []);          % Precision when reading the file {'double' (default), 'single'} (only for supported file formats)
                    
        
    % ==== COLORMAPS ====
    case 'colormap'
        template = struct(...
            'Name',             '', ...
            'CMap',             [], ...
            'isAbsoluteValues', 0, ...
            'isRealMin',        0, ...
            'DisplayColorbar',  1, ...
            'MaxMode',          'global', ...
            'MinValue',         [], ...
            'MaxValue',         [], ...
            'UseStatThreshold',  0, ...
            'Contrast',         0, ...
            'Brightness',        0);
        
    case 'colormapinfo'
        template.AllTypes     = {};
        template.Type         = '';
        template.DisplayUnits = '';

    % ==== GLOBAL DATA ====
    case 'measures'
        template = struct(...
            'DataType',        [], ...
            'F',               [], ...
            'Std',             [], ...
            'Time',            [], ...
            'SamplingRate',    [], ...
            'NumberOfSamples', [], ...
            'ChannelFlag',     [], ...
            'sFile',           [], ...
            'isModified',      0, ...
            'isFiltered',      0, ...
            'ColormapType',    '', ...
            'DisplayUnits',    [], ...
            'StatClusters',    [], ...
            'StatThreshUnder', [], ...
            'StatThreshOver',  []);

    case 'loadedresults'
        template = struct(...
            'FileName',         '', ...
            'DataType',         '', ...
            'Comment',          '', ...
            'Time',             [], ...
            'SamplingRate',     [], ...
            'NumberOfSamples',  [], ...
            'ChannelFlag',      [], ...
            'ImageGridAmp',     [], ...
            'ImagingKernel',    [], ...
            'Std',              [], ...
            'ZScore',           [], ...
            'SurfaceFile',      [], ...
            'HeadModelType',    [], ...
            'HeadModelFile',    [], ...
            'GridLoc',          [], ...
            'GridOrient',       [], ...
            'GridAtlas',        [], ...
            'grid2mri_interp',  [], ...
            'nComponents',      1, ...
            'GoodChannel',      [], ...
            'OpticalFlow',      [], ...
            'ColormapType',     '', ...
            'DisplayUnits',     [], ...
            'Atlas',            [], ...
            'StatClusters',     [], ...
            'StatThreshUnder',  [], ...
            'StatThreshOver',   [], ...
            'Function',         '');
        
    case 'loadeddipoles'
        template = struct(...
            'FileName',         '', ...
            'Comment',          '', ...
            'DataFile',         '', ...
            'Time',             [], ...
            'SamplingRate',     [], ...
            'NumberOfSamples',  [], ...
            'DipoleNames',      [], ...
            'Dipole',           [], ...
            'DisplayUnits',     [], ...
            'Subset',           [], ...
            'PreferredTimes',   []); 
        
    case 'loadedtimefreq'
        template = struct(...
            'FileName',         '', ...
            'Comment',          '', ...
            'DataFile',         '', ...
            'DataType',         '', ...
            'TF',               [], ...
            'TFmask',           [], ...
            'Freqs',            [], ...
            'Time',             [], ...
            'TimeBands',        [], ...
            'SamplingRate',     [], ...
            'NumberOfSamples',  [], ...
            'RefRowNames',      [], ...
            'RowNames',         [], ...
            'Modality',         [], ...
            'AllModalities',    [], ...
            'Measure',          [], ...
            'Method',           [], ...
            'Options',          [], ...
            'ColormapType',     '', ...
            'DisplayUnits',     [], ...
            'Atlas',            [], ...
            'GridLoc',          [], ...
            'GridAtlas',        [], ...
            'SurfaceFile',      [], ...
            'sPAC',             [], ...
            'StatClusters',     [], ...
            'StatThreshUnder',  [], ...
            'StatThreshOver',   []); 
        
    case 'loadedmatrix'
        template = struct(...
            'FileName',     '', ...
            'Comment',      '', ...
            'Description',  [], ...
            'DisplayUnits',     [], ...
            'SurfaceFile',  '', ...
            'Atlas',        [], ...
            'StatClusters', [], ...
            'StatThreshUnder',  [], ...
            'StatThreshOver',   []);
        
    case 'loadedsurface'
        template = struct(...
            'FileName',        '', ...
            'Name',            '', ...
            'Comment',         '', ...
            'Vertices',        [], ...
            'Faces',           [], ...
            'Color',           [], ...
            'VertConn',        [], ...
            'VertNormals',     [], ...
            'VertArea',        [], ...
            'SulciMap',        [], ...
            'tess2mri_interp', [], ...  % Interpolation matrix (Surface -> MRI)
            'mrimask',         [], ...  % MRI mask
            'envelope',        [], ...
            'Atlas',           db_template('Atlas'), ...
            'iAtlas',          1, ...
            'isAtlasModified', 0);
                
     
    case 'loadedmri'
        template = struct(...
            'FileName',   '', ...
            'Comment',    '', ...
            'Cube',       [], ...
            'Voxsize',    [], ...
            'SCS',        [], ...
            'NCS',        [], ...
            'Header',     [], ...
            'Histogram',  [], ...
            'InitTransf', [], ...     % Cell-array Nx2: {label, transf4x4}, label={'vox2ras', 'permute', 'flipdim', 'reg'}
            'Labels',     [], ...
            'History',    []);  

    case 'figureid'
        template = struct(...
            'Type',           '', ...
            'SubType',        '', ...
            'Modality',       '');
    
    case 'figure'
        template = struct(...
            'Id',                    db_template('FigureId'), ...   
            'hFigure',               0, ...
            'Handles',               struct(), ...
            'SelectedChannels',      []);

    case 'channeldesc'
        template = struct(...
                'Name',        '', ...
                'Comment',     '', ...
                'Type',        '', ...
                'Group',       [], ...
                'Loc',         [], ...
                'Orient',      [], ...
                'Weight',      []);

    case 'intraelectrode'
        template = struct(...
            'Name',  '', ...   % Identification
            'Type',  '', ...   % 'SEEG' / 'ECOG'
            'Model', '', ...   % String identifying the make and model
            'Loc',   [], ...   % [3xN] positions with all the relevant points (eg. tip and entry)
            'Color', [], ...   % [1x3] color vector
            'ContactNumber',   [], ...
            'ContactSpacing',  [], ...
            'ContactDiameter', [], ...
            'ContactLength',   [], ...
            'ElecDiameter',    [], ...
            'ElecLength',      [], ...
            'Visible',         1);

    case 'intracontact'
        template = struct(...
            'Name', '', ...    % Identification)
            'Loc',   []);      % [3x1] position for contact

    case 'dataset'
        template = struct(...
            'DataFile',    '', ...
            'StudyFile',   '', ...
            'SubjectFile', '', ...
            'ChannelFile', '', ...
            'Surfaces',    repmat(db_template('LoadedSurface'), 0), ...
            'Measures',    db_template('Measures'), ...
            'Results',     repmat(db_template('LoadedResults'), 0), ...
            'Dipoles',     repmat(db_template('LoadedDipoles'), 0), ...
            'Timefreq',    repmat(db_template('LoadedTimefreq'), 0), ...
            'Matrix',      repmat(db_template('LoadedMatrix'), 0), ...
            'Channel',     repmat(db_template('ChannelDesc'), 0), ...
            'MegRefCoef',  [], ...
            'Projector',   repmat(db_template('Projector'), 0), ...
            'Clusters',    repmat(db_template('Cluster'), 0), ...
            'IntraElectrodes', repmat(db_template('IntraElectrode'), 0), ...
            'isChannelModified', 0, ...
            'HeadPoints',  [], ...
            'Figure',      repmat(db_template('Figure'), 0));

    case 'displayhandlestimeseries'
        template = struct(...
            'hAxes',         [], ...
            'hCursor',       [], ...
            'hTextCursor',   [], ... 
            'hLines',        [], ...
            'hLinePatches',  [], ...
            'hLinesZeroY',   [], ...
            'hLineDecodingY',[], ...
            'LinesLabels',   [], ...
            'LinesColor',    [], ...
            'hColumnScale',  [], ...
            'hColumnScaleText', [], ...
            'hColumnScaleBar',  [], ...
            'ChannelOffsets',[], ...
            'DataMinMax',    [], ...
            'DisplayFactor', [], ...
            'DisplayUnits',  [], ...
            'DownsampleFactor', []);
    case 'displayhandlestopography'
        template = struct(...
            'hSurf',          [], ...
            'Wmat',           [], ... 
            'DataMinMax',     [], ...
            'hSensorMarkers', [], ...
            'hSensorLabels',  [], ...
            'hSensorOrient',  [], ...
            'hContours',      [], ...
            'MarkersLocs',    [], ...
            'hLines',         [], ...  % 2D Layout
            'ChannelOffsets', [], ...  % 2D Layout
            'DisplayFactor',  1, ...   % 2D Layout
            'hCursors',       [], ...  % 2D Layout
            'hZeroLines',     [], ...  % 2D Layout
            'hAxesLegend',    [], ...  % 2D Layout
            'hLabelLegend',   [], ...  % 2D Layout
            'hOverlayLegend', [], ...  % 2D Layout
            'Channel',        [], ...  % 2D Layout
            'Vertices',       [], ...  % 2D Layout
            'SelChan',        [], ...  % 2D Layout
            'BoxesCenters',   [], ...  % 2D Layout
            'LinesColor',     []);     % 2D Layout
    case 'displayhandles3dviz'
        template = struct(...
            'hSensorMarkers', [], ...
            'hSensorLabels',  [], ...
            'hSensorOrient',  [], ...
            'TensorDisplay',  []);
    case 'displayhandlesimage'
        template = struct(...
            'Data',            [], ...
            'Labels',          [], ...
            'iDims',           [], ...
            'DimLabels',       [], ...
            'DataMinMax',      [], ...
            'ShowLabels',      [], ...
            'ShortLabels',     0, ...
            'HideSelfConnect', 0, ...
            'PageName',        []);
    case 'displayhandlesvideo'
        template = struct(...
            'hPlayer',     [], ...   % PlayerType = 'VideoReader', 'WMPlayer'
            'hMedia',      [], ...   % PlayerType = 'WMPlayer'
            'hImage',      [], ...   % PlayerType = 'VideoReader'
            'PlayerType',  '', ...   % {'VideoReader', 'WMPlayer'}
            'VideoStart',  0);
    case 'displayhandlestimefreq'
        template = struct(...
            'DataMinMax', []);
    case 'montage'
        template = struct(...
            'Name',      '', ...
            'Type',      '', ...  % {'selection', 'text', 'matrix'}
            'Matrix',    [], ...
            'DispNames', [], ...
            'ChanNames', []);
        template.DispNames = {};
        template.ChanNames = {};
    case 'atlas'
        template = struct(...
            'Name',   'User scouts', ...
            'Scouts', repmat(db_template('Scout'), 0));
    % In mixed models, GridAtlas has a different structure, not well documented:
    % Vert2Grid      % set in process_inverse, but grid size does not match full grid (Scouts.GridRows): missing "all false" rows at end.
    % Grid2Source    % set in process_inverse
    % Scouts.Region  % 2nd letter is Surface/Volume/Dba/eXclude; 3rd (added in bst_headmodeler) is Constrained/Unconstrained/Loose  
    % Scouts.GridRows %
        
    case 'scout'
        template = struct(...
            'Vertices',    [], ... % Index of vertices
            'Seed',        [], ... % Initial vertex of the scout area
            'Color',       [], ...
            'Label',       '', ...
            'Function',    'Mean', ... % Scout function: PCA, FastPCA, Mean, Mean_norm, Max, Power, All
            'Region',      'UU', ...      % 1st letter: Left/Right/Unknown, 2nd letter: Frontal/Parietal/Temporal/Occipital/Central/Unkown
            'Handles',     repmat(struct( ...
                'hFig',        [], ... % Figure handle in which the scout is displayed
                'hScout',      [], ... % Handles to the graphical scout objects
                'hLabel',      [], ... 
                'hVertices',   [], ...
                'hPatch',      [], ...
                'hContour',    []), 0));
%     case 'scoutmat'
%         template = struct(...
%             'Vertices',   [], ... % Index of vertices
%             'Seed',       [], ... % Initial vertex of the scout area
%             'Color',      [0 1 0], ...
%             'Label',      '', ...
%             'Function',   'Mean', ...
%             'Region',     'UU');   % 1st letter: Left/Right/Unknown,  2nd letter: Frontal/Parietal/Temporal/Occipital/Central/Unkown
        
    case 'cluster'
        template = struct(...
            'Sensors',    '', ...   % File on which the scout is defined
            'Label',      '', ...   % Comment
            'Color',       [], ...  % [1x3] RGB values between 0 and 1
            'Function',   'Mean');  % Cluster function: PCA, FastPCA, Mean, Max, Power, All
           
    case 'globaldata'
        template = struct(...
            'Program', struct(...
                'Version',           [], ...
                'BrainstormHomeDir', [], ...
                'ScreenDef',         [], ...
                'DecorationSize',    [], ...
                'GuiLevel',          1, ...
                'GUI',               [], ...
                'CloneLock',         1, ...
                'isInternet',        0, ...
                'ProgressBar',       [], ...
                'ColormapPanels',    repmat(struct( ...
                    'ColormapType',  [], ...
                    'jPanel',        []),0), ...
                'Clipboard',         struct(...
                    'Nodes',         [], ...
                    'isCut',         0), ...
                'FontCache',         struct(), ...
                'PluginCache',       struct(), ...
                'ColorChooser',      [], ...
                'ProcessMenuCache',  struct(), ...
                'HasSigProcToolbox', []), ...
            'DataBase', struct(...
                'ProtocolInfo',       [], ...
                'ProtocolSubjects',   [], ...
                'ProtocolStudies',    [], ...
                'isProtocolLoaded',   [], ...
                'isProtocolModified', [], ...
                'iProtocol',          0, ...
                'BrainstormDbDir',    [], ...
                'DbVersion',          0, ...
                'isReadOnly',         0, ...
                'LastSavedTime',      0, ...
                'Searches',           struct(...
                    'iActive', 0, ...
                    'Active',  repmat(db_template('ActiveSearch'), 0), ...
                    'All',     repmat(struct(...
                        'Name',      '', ...
                        'Search',    []), 0))), ...
            'DataSet',  repmat(db_template('DataSet'), 0), ...
            'Mri',      repmat(db_template('LoadedMri'), 0), ...
            'Surface',  repmat(db_template('LoadedSurface'), 0), ...
            'Fibers',      repmat(db_template('LoadedFibers'), 0), ...
            'UserTimeWindow', struct(...
                'Time',            [], ...
                'SamplingRate',    [], ...
                'NumberOfSamples', 0, ...
                'CurrentTime',     []), ...
            'FullTimeWindow', struct(...
                'Epochs', repmat(struct(...
                    'Time',            [], ...
                    'NumberOfSamples', 0), 0), ...
                'CurrentEpoch',    []), ...
            'UserFrequencies', struct(...
                'Freqs',         [], ...
                'iCurrentFreq',  [], ...
                'HideFreqPanel', 0), ...
            'ChannelEditor',  struct(...
                'ChannelFile',    '', ...
                'ChannelMat',     [], ...
                'DataFile',       [], ...
                'LocColumns',     [], ...
                'OrientColumns',  [], ...
                'isModified',     0), ...
            'Guidelines', [], ...  % Temporary variables for the guidelines panels
            'HeadModeler',    struct(...
                'BFS',            [], ...
                'nbSpheres',      [], ...
                'GUI',            struct(...
                    'hFig',                     [], ...
                    'Figure3DButtonDown_Bak',   [], ...
                    'Figure3DButtonMotion_Bak', [], ...
                    'Figure3DButtonUp_Bak',     [], ...
                    'Figure3DCloseRequest_Bak', [], ...
                    'selectedButton',           [], ...
                    'mouseClicked',             [], ...
                    'isClosing',                [], ...
                    'hButtonTransX',            [], ...
                    'hButtonTransY',            [], ...
                    'hButtonTransZ',            [], ...
                    'hButtonResize',            [])), ...
             'CurrentFigure',        struct(...
                 'Type3D', [], ...
                 'Type2D', [], ...
                 'TypeTF', [], ...
                 'Last',   []), ...
             'DataViewer', struct(...
                 'SelectedRows',  [], ...
                 'DefaultFactor', []), ...
             'CurrentScoutsSurface', '', ...
             'VisualizationFilters', struct(...
                 'LowPassEnabled',       0, ...
                 'LowPassValue',         40, ...
                 'HighPassEnabled',      0, ...
                 'HighPassValue',        1, ...
                 'SinRemovalEnabled',    0, ...
                 'SinRemovalValue',      [], ...
                 'MirrorEnabled',        0, ...
                 'FullSourcesEnabled',   0), ...
             'Colormaps',            [], ...
             'Preferences',          [], ...
             'ChannelMontages', struct(...
                 'CurrentMontage', struct(), ...
                 'Montages', repmat(db_template('Montage'), 0)), ...
             'Processes', struct( ...
                 'All',       [], ...
                 'Current',   [], ...
                 'Pipelines', repmat(struct(...
                     'Name',      '', ...
                     'Processes', []), 0), ...
                 'Signature', []), ...
             'ProcessReports', struct(...
                 'jFrameReport', [], ...
                 'jTextHtml',    [], ...
                 'CurrentFile',  [], ...
                 'Reports',      []), ...
             'Interpolations', []);
        template.DataViewer.SelectedRows = {};
        
    case 'landmark'
        template = struct(...
            'Name',   '', ...
            'mriLoc', '');
    case 'scs'
        template = struct(...
            'NAS',    [], ...
            'LPA',    [], ...
            'RPA',    [], ...
            'R',      [], ...
            'T',      []);
    case 'ncs'
        template = struct(...
            'AC',    [], ...
            'PC',    [], ...
            'IH',    [], ...
            'R',     [], ...
            'T',     []);
        
    % ===== 3DViz appdata structure =====
    case 'tessinfo'
        anatomyColor = [.45*[1 1 1]; .6*[1 1 1]];
        template = struct(...
            'SurfaceFile',                '', ...
            'Name',                    '', ...
            'DataSource',              struct(...
                'Type',                '', ...   % {'data', 'results', 'stats', ...}
                'FileName',            '', ...
                'Atlas',               [], ...
                'GridAtlas',           [], ...
                'GridLoc',             [], ...
                'GridSmooth',          1), ...
            'ColormapType',            '', ...
            'hPatch',                  [], ...
            'nVertices',               0, ...
            'nFaces',                  0, ...
            'SurfAlpha',               0, ...    % Surface transparency
            'SurfShowSulci',           0, ...    % Toggle show/hide surface sulci map viewing   
            'SurfShowEdges',           0, ...    % Toggle oon/off surface edges display
            'AnatomyColor',            anatomyColor, ... % RGB color for sulci map encoding
            'SurfSmoothValue',         0, ...
            'Data',                    [], ...   % MEG, EEG or current density for overlay to anatomy (needs to be compatible in size with FaceVertexCdata patch property)
            'DataMinMax',              [], ...   % Minimum and maximum of the DataSource.FileName file
            'DataWmat',                [], ...   % Interpolation matrix (transformation to map Data on hPatch surface) 
            'OverlayCube',             [], ...   % Interpolated results in a MRI volume
            'OverlayCubeLabels',       [], ...   % Map of labels (volume of indices)
            'OverlayLabels',           [], ...   % Labels (cell-array of indices/strings)
            'isOverlayAtlas',          0, ...    % Is the loaded overlay the atlas described in the OverlayLabels
            'DataAlpha',               0, ...    % Alpha for blending of anatomy and surface data
            'DataThreshold',           0.5, ...  % Threshold to apply to color coding of data values  
            'SizeThreshold',           1, ...    % Threshold to apply to color coding of data values  
            'DataLimitValue',          [], ...   % Relative limits for colormapping
            'CutsPosition',            [0 0 0], ...   % Position of the three orthogonal MRI slices
            'Resect',                  [], ...       % 2 cells: Resect values [x,y,z] and resect sections {'left', 'right', 'struct', 'none'}
            'MipAnatomy',              [], ...        % 3 cells: Maximum intensity power in each direction (MRI amplitudes)
            'MipFunctional',           [], ...        % 3 cells: Maximum intensity power in each direction (sources amplitudes)
            'StatThreshOver',          [], ...
            'StatThreshUnder',         []);      
        template.Resect = {[0,0,0], 'none'};
        template.MipAnatomy = cell(3,1);
        template.MipFunctional = cell(3,1);
        
    case 'tsinfo'
        template = struct(...
            'FileName',        '', ...
            'Modality',        '', ...
            'DisplayMode',     '', ...
            'LinesLabels',     [], ...
            'AxesLabels',      [], ...
            'LinesColor',      [], ...
            'RowNames',        [], ...
            'MontageName',     [], ...
            'DefaultFactor',   [], ...
            'DisplayUnits',    [], ...
            'FlipYAxis',       0, ...
            'AutoScaleY',      1, ...
            'NormalizeAmp',    0, ...
            'Resolution',      [0 0], ...
            'YLabel',          [], ...
            'ShowEvents',      1, ...
            'ShowEventsMode',  'dot', ...
            'ShowLegend',      [], ...
            'ShowXGrid',       0, ...
            'ShowYGrid',       0, ...
            'ShowZeroLines',   1);

    case 'topoinfo'
        template = struct(...
            'FileName',      '', ...
            'Modality',      '', ...
            'FileType',      '', ...
            'TopoType',      '', ...
            'DataToPlot',    [], ...
            'UseSmoothing',  1);   
        
    case 'tfinfo'
        template = struct(...
            'FileName',        '', ...
            'Comment',         '', ...
            'DisplayMode',     '', ...
            'Function',        'power', ...
            'Normalized',      '', ...
            'FOOOFDisp',       'spectrum', ...
            'FOOOFDispRange',  0, ...
            'HideEdgeEffects', 0, ...
            'HighResolution',  0, ...
            'iFreqs',          [], ...
            'RowName',         [], ...
            'RefRowName',      [], ...
            'InputTarget',     [], ...
            'NeuronNames',     [], ...
            'DisplayAsDots',   0, ...
            'DisplayAsPhase',  0);
        
    case 'nodelist'
        template = struct(...
           'name',       '', ...
           'comment',    '', ...
           'type',       '', ...
           'jPanel',     [], ...
           'jBorder',    [], ...
           'jTree',      [], ...
           'jPanelTag',  [], ...
           'jCheckTag',  [], ...
           'contents',   [], ...
           'isStat',     0);
       
    case 'processfile'
        template = struct(...
            'iStudy',       0, ...
            'iItem',        0, ...
            'FileName',     '', ...
            'FileType',     '', ...
            'Comment',      '', ...
            'Condition',    '', ...
            'SubjectFile',  '', ...
            'SubjectName',  '', ...
            'DataFile',     '', ...
            'ChannelFile',  '', ...
            'ChannelTypes', '');
       
    case 'importfile'
        template = struct(...
            'iStudy',       0, ...
            'iItem',        0, ...
            'FileName',     '', ...
            'FileType',     'import', ...
            'Comment',      'import', ...
            'Condition',    'import', ...
            'SubjectFile',  '', ...
            'SubjectName',  '', ...
            'DataFile',     '', ...
            'ChannelFile',  '', ...
            'ChannelTypes', []);
        
    case 'processdesc'
        template = struct(...
            ... % Process description
            'Function',    [], ...
            'Comment',     '', ...
            'FileTag',     '', ...
            'Description', '', ...
            'Category',    '', ...
            'SubGroup',    '', ...
            'Index',       0, ...
            'isSeparator', 0, ...
            ... % Input type
            'InputTypes',  [], ...
            'OutputTypes', [], ...
            'nInputs',     1, ...
            'nOutputs',    1, ...
            'nMinFiles',   1, ...
            'isPaired',    0, ...
            'isSourceAbsolute', -1, ...
            'processDim',   [], ...
            'options',      [] ...
            );
        template.InputTypes = {};
        template.OutputTypes = {};
       
    case 'plugdesc'
        template = struct(...
            'Name',          '', ...  % Plugin name = subfolder in the Brainstorm user folder
            'Category',      '', ...  % Sub-menu in which the plugin is listed
            'Version',       '', ...  % String with the version name
            'AutoUpdate',     1, ...  % If 1, plugin is updated automatically when there is a new version available
            'AutoLoad',       0, ...  % If 1, plugin is loaded automatically at Brainstorm startup
            'URLzip',        '', ...  % Download URL (zip file accessible over HTTP/HTTPS/FTP)
            'URLinfo',       '', ...  % Information URL: Software website
            'ExtraMenus',    [], ...  % Cell matrix {Nx2} with list of entries to add to the plugins menu, eg. {'Download page', 'web(''http://...'')'; 'Tutorial', 'web(''http://...'')'}
            'TestFile',      '', ...  % Function/file name to check the existence of the plugin outside of the Brainstorm user folder
            'ReadmeFile',    '', ...  % Text filename (relative to the plugin path) - If empty, try using brainstorm3/doc/plugin/<Name>_readme.txt
            'LogoFile',      '', ...  % Logo filename (relative to the plugin path) - If empty, try using brainstorm3/doc/plugin/<Name>_logo.[gif|png]
            'MinMatlabVer',   0, ...  % Minimum Matlab version, as returned by bst_get('MatlabVersion')
            'CompiledStatus', 0, ...  % 0=Not available in the compiled version, 1=Available for download, 2=Compiled with Brainstorm
            'RequiredPlugs', [], ...  % Cell-array of required plugin names, to install/load before this one: {Nx2}=>{'plugname','version';...} or {Nx1}=>{'plugname';...}
            'UnloadPlugs',   [], ...  % Cell-array of incompatible plugin names, to remove from path before adding
            'LoadFolders',   [], ...  % Cell-array of subfolders to add to the path when setting the plugin up (use {'*'} to load all subfolders)
            'GetVersionFcn', [], ...  % String to eval to get the version (after installation)
            'DownloadedFcn', [], ...  % String to eval or function handle to call after downloading the plugin
            'InstalledFcn',  [], ...  % String to eval or function handle to call after installing the plugin
            'UninstalledFcn',[], ...  % String to eval or function handle to call after uninstalling the plugin
            'LoadedFcn',     [], ...  % String to eval or function handle to call after loading the plugin
            'UnloadedFcn',   [], ...  % String to eval or function handle to call after unloading the plugin
            'DeleteFiles',   [], ...  % Cell-array of files to delete after unzipping the plugin package (path relative to the plugin folder)
            'DeleteFilesBin',[], ...  % Cell-array of files to delete before compiling Brainstorm, to avoid including them in the binary distribution (path relative to the plugin folder) 
            ... % Set when installing or loading the plugin
            'InstallDate',   '', ...  % Installation date
            'SubFolder',     '', ...  % If all the code is in a subfolder: detect this at installation time
            'Path',          [], ...  % Set at runtime: Installation path for this plugin
            'Processes',     [], ...  % List of process functions to be added to the pipeline manager
            'isLoaded',      0, ...   % Set at runtime: 0=Not loaded, 1=Loaded (folder and specific subfolders added to Matlab path)
            'isManaged',     0);      % Set at runtime: 0=Installed by the user, 1=Installed automatically by Brainstorm
        template.LoadFolders = {};
        template.UnloadPlugs = {};
        template.RequiredPlugs = {};
        template.Processes = {};
        
    case 'interpolation'
        template = struct(...
            'WInterp',   [], ...
            'Signature', []);
    
    case 'activesearch'
        template = struct(...
            'Name', [], ...
            'SearchNode', [], ...
            'AnatRootNode', [], ...
            'AnatSelNode',  [], ...
            'AnatNumNodes', 0, ...
            'FuncSubjRootNode', [], ...
            'FuncSubjSelNode',  [], ...
            'FuncSubjNumNodes', 0, ...
            'FuncCondRootNode', [], ...
            'FuncCondSelNode',  [], ...
            'FuncCondNumNodes', 0);
    
    case 'searchparam'
        template = struct(...
            'Value',         [], ...
            'SearchType',    1, ...
                ... % 1 = file name (comment)
                ... % 2 = file type
                ... % 3 = file path (filename on harddrive)
                ... % 4 = parent name (comment of any parent)
            'EqualityType',  1, ...
                ... % 1 = contains
                ... % 2 = equals
            'CaseSensitive', 0);
        
    case 'searchnode'
        template = struct(...
            'Type',     0, ...
                ... % 0 = null
                ... % 1 = db_template('SearchParam') value
                ... % 2 = Boolean value (1 = AND, 2 = OR, 3 = NOT)
                ... % 3 = Parent node (no value, only children)
                ...     % Exception: value of 1 = hide parent nodes in GUI
            'Value',    [], ...
            'Children', []);

    case 'loadedfibers'
        template = struct(...
            'FileName', '', ...
            'Comment',  [], ...
            'Header',   [], ...
            'Points',   [], ...
            'Colors',   [], ...
            'Scouts', struct(...
                'ConnectFile', [], ...
                'Assignment',  []), ...
            'History',  []);
        
    otherwise
        error('Unknown data template : %s', structureName);
end
  
  


